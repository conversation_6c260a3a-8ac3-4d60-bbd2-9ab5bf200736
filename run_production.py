"""
ملف تشغيل محسن للإنتاج - GESTION DES FORMATIONS
يدعم الشبكة المحلية وقاعدة البيانات المركزية
"""

import os
import sys
import socket
import threading
import time
import webbrowser
from pathlib import Path
import io

# إصلاح مشكلة stdin/stdout في الملف التنفيذي
if getattr(sys, 'frozen', False):
    # إذا كان التطبيق مجمد (EXE)
    application_path = os.path.dirname(sys.executable)
    os.chdir(application_path)

    # إصلاح stdin/stdout للملف التنفيذي
    if sys.stdin is None:
        sys.stdin = io.StringIO()
    if sys.stdout is None:
        sys.stdout = io.StringIO()
    if sys.stderr is None:
        sys.stderr = io.StringIO()
else:
    # إذا كان يعمل من Python
    application_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, application_path)

# تعيين متغيرات البيئة للإنتاج
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = 'False'

# إنشاء مجلد البيانات إذا لم يكن موجوداً
data_dir = os.path.join(application_path, 'data')
if not os.path.exists(data_dir):
    os.makedirs(data_dir)

# تعيين مسار قاعدة البيانات المركزية
db_path = os.path.join(data_dir, 'gestion_formations.db')
os.environ['DATABASE_URL'] = f'sqlite:///{db_path}'

print("🚀 GESTION DES FORMATIONS - نظام إدارة التكوين")
print("=" * 60)
print(f"📍 مسار التطبيق: {application_path}")
print(f"💾 قاعدة البيانات: {db_path}")

try:
    from app import create_app
    print("✅ تم تحميل التطبيق بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل التطبيق: {e}")
    print("💡 تأكد من وجود مجلد app وملفاته")
    input("اضغط Enter للخروج...")
    sys.exit(1)

# إنشاء التطبيق
app = create_app()

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # الاتصال بخادم خارجي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        # في حالة فشل الطريقة الأولى
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception:
            return "127.0.0.1"

def check_port_available(port):
    """التحقق من توفر المنفذ"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.bind(('', port))
        s.close()
        return True
    except OSError:
        return False

def find_available_port(start_port=5000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        if check_port_available(port):
            return port
    return None

def open_browser(url):
    """فتح المتصفح بعد تأخير"""
    time.sleep(2)  # انتظار حتى يبدأ الخادم
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح: {url}")
    except Exception as e:
        print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")
        print(f"💡 افتح المتصفح يدوياً واذهب إلى: {url}")

def show_network_info(local_ip, port):
    """عرض معلومات الشبكة"""
    print("\n🌐 معلومات الشبكة:")
    print(f"   📍 الخادم المحلي: http://{local_ip}:{port}")
    print(f"   📍 الوصول المحلي: http://localhost:{port}")
    print(f"   📍 الوصول المحلي: http://127.0.0.1:{port}")
    
    print("\n👥 للوصول من أجهزة أخرى في الشبكة المحلية:")
    print(f"   🔗 استخدم: http://{local_ip}:{port}")
    
    print("\n🔧 إعدادات جدار الحماية:")
    print(f"   • تأكد من السماح للبرنامج بالوصول للشبكة")
    print(f"   • تأكد من فتح المنفذ {port} في جدار الحماية")
    
    print("\n💾 قاعدة البيانات المركزية:")
    print(f"   📁 المسار: {db_path}")
    print("   🔄 جميع المستخدمين يصلون لنفس البيانات")

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        with app.app_context():
            from app.models import db
            
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()
            print("✅ تم تهيئة قاعدة البيانات")
            
            # التحقق من وجود مستخدم إداري
            from app.models import User
            admin = User.query.filter_by(username='admin').first()
            
            if not admin:
                print("👤 إنشاء مستخدم إداري افتراضي...")
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الإداري:")
                print("   👤 اسم المستخدم: admin")
                print("   🔑 كلمة المرور: admin123")
                print("   ⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول")
            
    except Exception as e:
        print(f"⚠️ تحذير في تهيئة قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        # الحصول على عنوان IP المحلي
        local_ip = get_local_ip()

        # البحث عن منفذ متاح
        port = find_available_port(5000)
        if port is None:
            print("❌ لم يتم العثور على منفذ متاح")
            if not getattr(sys, 'frozen', False):
                input("اضغط Enter للخروج...")
            return

        # عرض معلومات الشبكة
        show_network_info(local_ip, port)

        # تهيئة قاعدة البيانات
        initialize_database()

        # إعداد URL للمتصفح
        url = f"http://{local_ip}:{port}"

        # بدء thread لفتح المتصفح
        browser_thread = threading.Thread(target=open_browser, args=(url,))
        browser_thread.daemon = True
        browser_thread.start()

        print(f"\n🚀 بدء الخادم على المنفذ {port}...")
        print("⏹️  اضغط Ctrl+C للإيقاف")
        print("=" * 60)

        # تشغيل الخادم مع إعدادات خاصة للملف التنفيذي
        app.run(
            host='0.0.0.0',  # السماح بالوصول من جميع العناوين
            port=port,
            debug=False,     # إيقاف وضع التطوير
            threaded=True,   # دعم عدة مستخدمين
            use_reloader=False,  # إيقاف إعادة التحميل التلقائي
            passthrough_errors=True  # تمرير الأخطاء
        )

    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        # تجنب استخدام input() في الملف التنفيذي
        if not getattr(sys, 'frozen', False):
            input("اضغط Enter للخروج...")
        else:
            time.sleep(3)  # انتظار 3 ثوان بدلاً من input

if __name__ == '__main__':
    main()
