@echo off
chcp 65001 > nul
title GESTION DES FORMATIONS - إنشاء مثبت Windows

echo.
echo ========================================
echo   GESTION DES FORMATIONS
echo   إنشاء مثبت Windows احترافي
echo   ABOULFADEL.A
echo ========================================
echo.

echo 🎯 هذا السكريبت سيقوم بـ:
echo    ✅ فحص وجود Inno Setup
echo    ✅ فحص الملفات المطلوبة
echo    ✅ بناء مثبت Windows احترافي
echo    ✅ إنشاء ملف setup.exe
echo.

pause

echo.
echo 🔧 بدء عملية إنشاء المثبت...
python create_installer.py

echo.
echo 🏁 انتهت عملية إنشاء المثبت
echo.

if exist "Output\GESTION_DES_FORMATIONS_Setup_v1.0.0.exe" (
    echo 🎉 تم إنشاء المثبت بنجاح!
    echo.
    echo 📦 ملف المثبت:
    echo    Output\GESTION_DES_FORMATIONS_Setup_v1.0.0.exe
    echo.
    echo 💡 يمكنك الآن:
    echo    • نسخ ملف المثبت إلى أي جهاز Windows
    echo    • تشغيله بصلاحيات المدير
    echo    • اتباع خطوات التثبيت
    echo.
    echo 🌐 للشبكة المحلية:
    echo    • اختر "إضافة استثناء في جدار الحماية" أثناء التثبيت
    echo    • شغل البرنامج على الخادم الرئيسي
    echo    • استخدم عنوان IP المعروض للوصول من أجهزة أخرى
    echo.
) else (
    echo ❌ فشل في إنشاء المثبت
    echo 💡 تحقق من:
    echo    • وجود Inno Setup مثبت
    echo    • وجود الملف التنفيذي في GESTION_DES_FORMATIONS_Distribution
    echo    • صلاحيات الكتابة في مجلد Output
    echo.
)

pause
