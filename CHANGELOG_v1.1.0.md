# GESTION DES FORMATIONS - Changelog v1.1.0

## 🎉 Nouvelles fonctionnalités ajoutées

### 1. 📋 Nouveau module "Dossiers Administratif"
- **Nouveau modèle de données** : `DossierAdministratif`
- **Documents administratifs** avec cases à cocher :
  - F1
  - RC (Registre de Commerce)
  - Statut
  - Déclaration sur l'honneur
  - Pv (Procès-verbal)
  - Procuration
  - Attestation de RIB
- **Interface complète** : Ajouter, modifier, supprimer, lister
- **Intégration** dans la navigation principale

### 2. 🔧 Améliorations du module "Dossiers de Remboursement"
- **Nouveau champ F3** ajouté aux formulaires
- **Mise à jour** des templates d'ajout et de modification
- **Base de données** mise à jour automatiquement

### 3. 🌐 Amélioration de la page "Éligibilité OFPPT"
- **Nouveau design** avec deux boutons distincts :
  - **Éligibilité OFPPT** : Lien vers le portail d'éligibilité
  - **Contrats spéciaux de formation (CSF)** : Lien vers les informations CSF
- **Interface améliorée** avec cartes séparées et icônes

### 4. 📊 Optimisation du tableau de bord
- **Suppression** des sections non utilisées :
  - Répartition par Type
  - Évolution des Inscriptions
  - Activités Récentes
- **Interface plus épurée** et focalisée sur l'essentiel

### 5. 🗂️ Réorganisation de la navigation
- **Ordre mis à jour** dans la barre latérale :
  1. Tableau de bord
  2. Fiches d'Inscription
  3. **Dossiers Administratif** (nouveau)
  4. Dossiers Techniques
  5. Dossiers de Remboursement
  6. Éligibilité OFPPT

### 6. 🌍 Localisation française complète
- **Installateur** entièrement en français
- **Guide d'utilisation** (`lisez-moi.txt`) en français
- **Messages d'installation** traduits
- **Cohérence linguistique** dans tout le système

## 🔧 Améliorations techniques

### Base de données
- **Nouveau modèle** : `DossierAdministratif` avec relations appropriées
- **Migration automatique** pour ajouter le champ F3
- **Script de mise à jour** : `update_database.py`

### Interface utilisateur
- **Templates HTML** pour le nouveau module
- **Formulaires WTForms** pour la validation
- **Routes Flask** complètes (CRUD)
- **Intégration** dans le système de permissions

### Installateur Windows
- **Textes français** dans l'installateur Inno Setup
- **Guide utilisateur** en français
- **Compatibilité** maintenue avec toutes les versions Windows

## 📁 Nouveaux fichiers ajoutés

### Templates
- `app/templates/dossiers_administratif/index.html`
- `app/templates/dossiers_administratif/add.html`
- `app/templates/dossiers_administratif/edit.html`

### Documentation
- `GESTION_DES_FORMATIONS_Distribution/lisez-moi.txt`
- `update_database.py`
- `CHANGELOG_v1.1.0.md`

### Modèles et formulaires
- Nouveau modèle `DossierAdministratif` dans `models.py`
- Nouveau formulaire `DossierAdministratifForm` dans `forms.py`
- Routes complètes dans `routes.py`

## 🚀 Instructions de mise à jour

### Pour les développeurs
1. **Mettre à jour la base de données** :
   ```bash
   python update_database.py
   ```

2. **Reconstruire l'exécutable** :
   ```bash
   python build_now.py
   ```

3. **Créer le nouvel installateur** :
   ```bash
   python create_installer.py
   ```

### Pour les utilisateurs finaux
1. **Télécharger** le nouvel installateur : `GESTION_DES_FORMATIONS_Setup_v1.0.0.exe`
2. **Désinstaller** l'ancienne version (optionnel)
3. **Installer** la nouvelle version
4. **Lancer** l'application - la base de données sera mise à jour automatiquement

## ✅ Tests effectués

- ✅ **Installation** sur Windows 10/11
- ✅ **Fonctionnalité** Dossiers Administratif
- ✅ **Champ F3** dans Dossiers de Remboursement
- ✅ **Navigation** mise à jour
- ✅ **Liens OFPPT** fonctionnels
- ✅ **Compatibilité** base de données existante
- ✅ **Interface** en français

## 🔄 Compatibilité

- **Windows** : 7, 8, 8.1, 10, 11 (32/64 bits)
- **Base de données** : Migration automatique depuis v1.0.0
- **Données existantes** : Préservées lors de la mise à jour

## 📞 Support

Pour toute question ou problème :
- **Développeur** : ABOULFADEL.A
- **Version** : 1.1.0
- **Date** : Juillet 2025

---

© 2024-2025 ABOULFADEL.A - Tous droits réservés
