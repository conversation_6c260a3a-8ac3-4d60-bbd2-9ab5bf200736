# دليل البناء والتثبيت - GESTION DES FORMATIONS

## نظام إدارة التكوين والتدريب
**المطور**: ABOULFADEL.A  
**الإصدار**: 1.0.0  
**التاريخ**: 2024

---

## 📋 المحتويات

1. [متطلبات النظام](#متطلبات-النظام)
2. [خطوات البناء](#خطوات-البناء)
3. [إنشاء المثبت](#إنشاء-المثبت)
4. [التثبيت والتشغيل](#التثبيت-والتشغيل)
5. [إعداد الشبكة المحلية](#إعداد-الشبكة-المحلية)
6. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🖥️ متطلبات النظام

### أنظمة التشغيل المدعومة
- ✅ Windows 7 SP1 (32/64 بت)
- ✅ Windows 8 (32/64 بت)
- ✅ Windows 8.1 (32/64 بت)
- ✅ Windows 10 (32/64 بت)
- ✅ Windows 11 (32/64 بت)

### متطلبات التطوير
- Python 3.7 أو أحدث
- pip (مدير حزم Python)
- مساحة قرص: 500 MB على الأقل
- ذاكرة: 2 GB RAM على الأقل

### متطلبات التشغيل (للمستخدم النهائي)
- لا يحتاج Python أو أي برامج إضافية
- مساحة قرص: 100 MB
- ذاكرة: 512 MB RAM

---

## 🏗️ خطوات البناء

### 1. فحص التوافق
```bash
python test_compatibility.py
```
هذا سيفحص توافق النظام ويُنشئ تقرير مفصل.

### 2. إنشاء الأيقونة
```bash
python create_icon.py
```
يحول الشعار إلى أيقونة ICO للاستخدام في الملف التنفيذي.

### 3. بناء الملف التنفيذي
```bash
python build_final_exe.py
```

أو استخدم الطريقة المبسطة:
```bash
python build_simple_exe.py
```

### 4. النتيجة
بعد البناء الناجح ستجد:
- `GESTION_DES_FORMATIONS_Distribution/` - حزمة التوزيع الكاملة
- `dist/GESTION_DES_FORMATIONS.exe` - الملف التنفيذي

---

## 📦 إنشاء المثبت

### متطلبات إضافية
- [Inno Setup](https://jrsoftware.org/isinfo.php) (مجاني)

### خطوات إنشاء المثبت
1. تأكد من وجود حزمة التوزيع
2. ثبت Inno Setup
3. افتح ملف `installer_config.iss`
4. اضغط Build أو F9
5. ستجد المثبت في مجلد `Output/`

---

## 🚀 التثبيت والتشغيل

### للمطور (اختبار محلي)
```bash
python run_production.py
```

### للمستخدم النهائي

#### الطريقة الأولى: استخدام المثبت
1. شغل `GESTION_DES_FORMATIONS_Setup.exe`
2. اتبع خطوات التثبيت
3. شغل البرنامج من قائمة البرامج

#### الطريقة الثانية: نسخ مباشر
1. انسخ مجلد `GESTION_DES_FORMATIONS_Distribution`
2. شغل `GESTION_DES_FORMATIONS.exe`
3. أو شغل `تشغيل_النظام.bat`

---

## 🌐 إعداد الشبكة المحلية

### على الخادم الرئيسي
1. شغل `GESTION_DES_FORMATIONS.exe`
2. لاحظ عنوان IP المعروض (مثل: *************:5000)
3. تأكد من فتح المنفذ 5000 في جدار الحماية

### على الأجهزة الأخرى
1. افتح المتصفح
2. اذهب إلى عنوان IP الخادم (مثل: http://*************:5000)
3. ستصل لنفس النظام والبيانات

### إعدادات جدار الحماية Windows
```cmd
netsh advfirewall firewall add rule name="GESTION DES FORMATIONS" dir=in action=allow program="C:\Path\To\GESTION_DES_FORMATIONS.exe"
netsh advfirewall firewall add rule name="GESTION DES FORMATIONS Port" dir=in action=allow protocol=TCP localport=5000
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### البرنامج لا يبدأ
- تأكد من تشغيله بصلاحيات المدير
- تحقق من وجود مساحة كافية على القرص
- شغل `test_compatibility.py` للتشخيص

#### لا يمكن الوصول من الشبكة
- تحقق من إعدادات جدار الحماية
- تأكد من أن الأجهزة في نفس الشبكة
- جرب عنوان IP مختلف

#### قاعدة البيانات لا تعمل
- تحقق من صلاحيات الكتابة في مجلد `data`
- تأكد من عدم استخدام قاعدة البيانات من برنامج آخر

#### أداء بطيء
- أغلق البرامج غير الضرورية
- تأكد من وجود ذاكرة كافية
- استخدم قرص SSD إذا أمكن

---

## 📁 هيكل الملفات

```
GESTION_DES_FORMATIONS_Distribution/
├── GESTION_DES_FORMATIONS.exe    # الملف التنفيذي الرئيسي
├── تشغيل_النظام.bat              # سكريبت التشغيل
├── اقرأني.txt                    # دليل الاستخدام
├── data/                         # قاعدة البيانات (تُنشأ تلقائياً)
├── backups/                      # النسخ الاحتياطية (تُنشأ تلقائياً)
├── logs/                         # ملفات السجل (تُنشأ تلقائياً)
└── images/                       # الشعارات والأيقونات
    ├── Formation-continue-1024x1024.png
    ├── app_icon.ico
    └── favicon.ico
```

---

## 👤 المستخدم الافتراضي

عند أول تشغيل، سيتم إنشاء مستخدم إداري:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: يرجى تغيير كلمة المرور بعد أول تسجيل دخول!

---

## 📞 الدعم الفني

للمساعدة أو الاستفسارات:
- راجع ملف `اقرأني.txt` في حزمة التوزيع
- شغل `test_compatibility.py` للتشخيص
- تحقق من ملفات السجل في مجلد `logs`

---

## 📄 الترخيص

© 2024 ABOULFADEL.A - جميع الحقوق محفوظة

---

**ملاحظة**: هذا الدليل يغطي جميع جوانب البناء والتثبيت. للحصول على أفضل النتائج، اتبع الخطوات بالترتيب المذكور.
