"""
سكريبت البناء النهائي - GESTION DES FORMATIONS
إنشاء ملف تنفيذي شامل يدعم جميع إصدارات Windows
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_header():
    """طباعة رأس البرنامج"""
    print("🏗️ " + "=" * 58)
    print("   GESTION DES FORMATIONS - مُنشئ الملف التنفيذي")
    print("   نظام إدارة التكوين والتدريب")
    print("   ABOULFADEL.A")
    print("=" * 60)
    print()
    print("🎯 المتطلبات المدعومة:")
    print("   ✅ Windows 7, 8, 8.1, 10, 11")
    print("   ✅ أنظمة 32 و 64 بت")
    print("   ✅ الشبكة المحلية (LAN)")
    print("   ✅ عدة مستخدمين متزامنين")
    print("   ✅ قاعدة بيانات مركزية")
    print("   ✅ لا يحتاج Python أو برامج إضافية")
    print()

def check_system():
    """فحص النظام والمتطلبات"""
    print("🔍 فحص النظام...")
    
    # فحص Python
    python_version = sys.version_info
    print(f"🐍 Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    
    # فحص الملفات الأساسية
    required_files = [
        'run.py',
        'config.py', 
        'requirements.txt',
        'app/__init__.py',
        'app/models.py',
        'app/routes.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} غير موجود")
            return False
    
    # فحص الشعار
    logo_path = "app/static/images/Formation-continue-1024x1024.png"
    if os.path.exists(logo_path):
        print(f"✅ الشعار: {logo_path}")
    else:
        print(f"⚠️ الشعار غير موجود: {logo_path}")
    
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    # قائمة المكتبات المطلوبة
    requirements = [
        'pyinstaller>=6.0.0',
        'Pillow>=9.0.0',
        'Flask==2.2.2',
        'Flask-Login==0.6.2',
        'Flask-SQLAlchemy==3.0.2',
        'Flask-WTF==1.1.1',
        'Werkzeug==2.2.2'
    ]
    
    for req in requirements:
        try:
            print(f"📥 تثبيت {req}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", req
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {req}")
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {req}")
    
    # التحقق من PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller جاهز")
        return True
    except ImportError:
        print("❌ PyInstaller غير متاح")
        return False

def create_icon():
    """إنشاء الأيقونة"""
    print("\n🎨 إنشاء الأيقونة...")
    
    try:
        # تشغيل سكريبت إنشاء الأيقونة
        result = subprocess.run([
            sys.executable, "create_icon.py"
        ], capture_output=True, text=True)
        
        icon_path = "app/static/images/app_icon.ico"
        if os.path.exists(icon_path):
            print("✅ تم إنشاء الأيقونة")
            return icon_path
        else:
            print("⚠️ لم يتم إنشاء الأيقونة")
            return None
            
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الأيقونة: {e}")
        return None

def create_spec_file(icon_path=None):
    """إنشاء ملف spec محسن"""
    print("\n📝 إنشاء ملف التكوين...")
    
    icon_line = f"icon='{icon_path}'," if icon_path else "# icon=None,"
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين PyInstaller - GESTION DES FORMATIONS
يدعم جميع إصدارات Windows والشبكة المحلية
"""

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# مسار المشروع
project_path = os.path.abspath('.')

# جمع ملفات Flask
flask_datas = collect_data_files('flask')
jinja2_datas = collect_data_files('jinja2')
wtforms_datas = collect_data_files('wtforms')

# ملفات التطبيق
app_datas = [
    ('app', 'app'),
    ('config.py', '.'),
    ('config_production.py', '.'),
]

# إضافة قاعدة البيانات إذا كانت موجودة
if os.path.exists('app.db'):
    app_datas.append(('app.db', '.'))

# جمع جميع الملفات
all_datas = flask_datas + jinja2_datas + wtforms_datas + app_datas

# المكتبات المخفية
hiddenimports = [
    'flask', 'flask.app', 'flask.blueprints', 'flask.globals',
    'flask.helpers', 'flask.json', 'flask.logging', 'flask.sessions',
    'flask.signals', 'flask.templating', 'flask.testing', 'flask.views',
    'flask.wrappers', 'werkzeug', 'werkzeug.serving', 'werkzeug.utils',
    'werkzeug.exceptions', 'werkzeug.routing', 'werkzeug.security',
    'jinja2', 'jinja2.ext', 'jinja2.loaders', 'jinja2.runtime',
    'jinja2.utils', 'sqlalchemy', 'sqlalchemy.dialects.sqlite',
    'sqlalchemy.engine', 'sqlalchemy.orm', 'sqlalchemy.pool',
    'flask_sqlalchemy', 'flask_login', 'flask_wtf', 'wtforms',
    'wtforms.fields', 'wtforms.validators', 'wtforms.widgets',
    'email_validator', 'sqlite3', 'threading', 'webbrowser',
    'socket', 'time', 'datetime', 'json', 'uuid', 'hashlib',
    'secrets', 'pathlib', 'shutil'
]

# تحليل الملف الرئيسي
a = Analysis(
    ['run_production.py'],
    pathex=[project_path],
    binaries=[],
    datas=all_datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy',
        'PIL.ImageTk', 'PIL.ImageWin', 'test', 'unittest',
        'doctest', 'pdb', 'pydoc', 'distutils'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات المكررة
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GESTION_DES_FORMATIONS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    {icon_line}
    version_info={{
        'version': '*******',
        'description': 'GESTION DES FORMATIONS - نظام إدارة التكوين',
        'product_name': 'GESTION DES FORMATIONS',
        'product_version': '1.0.0',
        'company_name': 'ABOULFADEL.A',
        'file_description': 'نظام إدارة التكوين والتدريب',
        'internal_name': 'GestionFormations',
        'copyright': '© 2024 ABOULFADEL.A',
        'original_filename': 'GESTION_DES_FORMATIONS.exe',
    }}
)
'''
    
    with open('gestion_formations_final.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف التكوين")
    return 'gestion_formations_final.spec'

def clean_build():
    """تنظيف ملفات البناء السابقة"""
    print("\n🧹 تنظيف ملفات البناء السابقة...")

    dirs_to_clean = ['dist', 'build', '__pycache__']
    files_to_clean = ['*.spec']

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ تم حذف {dir_name}")

    print("✅ تم التنظيف")

def build_exe(spec_file):
    """بناء الملف التنفيذي"""
    print(f"\n🔨 بناء الملف التنفيذي...")
    print("⏳ هذا قد يستغرق عدة دقائق...")

    start_time = time.time()

    try:
        # تشغيل PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]

        print("🚀 بدء عملية البناء...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        end_time = time.time()
        duration = end_time - start_time

        if result.returncode == 0:
            print(f"✅ تم البناء بنجاح في {duration:.1f} ثانية")
            return True
        else:
            print("❌ فشل في البناء")
            print("📋 رسائل الخطأ:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ خطأ في عملية البناء: {e}")
        return False

def check_result():
    """فحص نتيجة البناء"""
    print("\n🔍 فحص النتيجة...")

    exe_path = "dist/GESTION_DES_FORMATIONS.exe"

    if os.path.exists(exe_path):
        # حساب الحجم
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)

        print(f"🎉 الملف التنفيذي تم إنشاؤه بنجاح!")
        print(f"📍 المسار: {os.path.abspath(exe_path)}")
        print(f"📊 الحجم: {size_mb:.1f} MB")

        # فحص الملفات المرافقة
        dist_files = os.listdir('dist')
        print(f"📁 ملفات التوزيع: {len(dist_files)} ملف")

        return True
    else:
        print("❌ الملف التنفيذي غير موجود")
        return False

def create_distribution():
    """إنشاء حزمة التوزيع"""
    print("\n📦 إنشاء حزمة التوزيع...")

    # إنشاء مجلد التوزيع
    dist_dir = "GESTION_DES_FORMATIONS_Distribution"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)

    os.makedirs(dist_dir)

    # نسخ الملف التنفيذي
    exe_source = "dist/GESTION_DES_FORMATIONS.exe"
    exe_dest = os.path.join(dist_dir, "GESTION_DES_FORMATIONS.exe")

    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print("✅ تم نسخ الملف التنفيذي")

    # إنشاء ملفات مساعدة
    create_helper_files(dist_dir)

    print(f"✅ تم إنشاء حزمة التوزيع: {dist_dir}")
    return dist_dir

def create_helper_files(dist_dir):
    """إنشاء ملفات مساعدة"""

    # ملف تشغيل الخادم
    server_script = '''@echo off
chcp 65001 > nul
title GESTION DES FORMATIONS - نظام إدارة التكوين

echo.
echo ========================================
echo   GESTION DES FORMATIONS
echo   نظام إدارة التكوين والتدريب
echo   ABOULFADEL.A
echo ========================================
echo.

echo 🚀 بدء تشغيل النظام...
echo.

GESTION_DES_FORMATIONS.exe

pause
'''

    with open(os.path.join(dist_dir, 'تشغيل_النظام.bat'), 'w', encoding='utf-8') as f:
        f.write(server_script)

    # ملف معلومات
    readme_content = '''# GESTION DES FORMATIONS
## نظام إدارة التكوين والتدريب

### معلومات النظام
- **الاسم**: GESTION DES FORMATIONS
- **الإصدار**: 1.0.0
- **المطور**: ABOULFADEL.A
- **التاريخ**: 2024

### متطلبات التشغيل
✅ Windows 7, 8, 8.1, 10, 11
✅ أنظمة 32 و 64 بت
✅ لا يحتاج Python أو برامج إضافية
✅ يدعم الشبكة المحلية (LAN)
✅ يدعم عدة مستخدمين متزامنين

### كيفية التشغيل
1. شغل ملف `GESTION_DES_FORMATIONS.exe`
2. أو شغل `تشغيل_النظام.bat`
3. سيفتح النظام في المتصفح تلقائياً

### للوصول من الشبكة المحلية
- شغل البرنامج على الخادم الرئيسي
- استخدم عنوان IP المعروض للوصول من أجهزة أخرى
- مثال: http://*************:5000

### قاعدة البيانات
- قاعدة البيانات مركزية على الجهاز الذي يشغل البرنامج
- جميع المستخدمين يصلون لنفس البيانات
- يتم إنشاء مجلد `data` تلقائياً لحفظ البيانات

### المستخدم الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- يرجى تغيير كلمة المرور بعد أول تسجيل دخول

### الدعم الفني
للمساعدة أو الاستفسارات، يرجى التواصل مع المطور.
'''

    with open(os.path.join(dist_dir, 'اقرأني.txt'), 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✅ تم إنشاء الملفات المساعدة")

def main():
    """الدالة الرئيسية"""
    print_header()

    # فحص النظام
    if not check_system():
        print("\n❌ فشل في فحص النظام")
        input("اضغط Enter للخروج...")
        return False

    # تثبيت المتطلبات
    if not install_requirements():
        print("\n❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return False

    # إنشاء الأيقونة
    icon_path = create_icon()

    # تنظيف ملفات البناء السابقة
    clean_build()

    # إنشاء ملف spec
    spec_file = create_spec_file(icon_path)

    # بناء الملف التنفيذي
    if not build_exe(spec_file):
        print("\n❌ فشل في بناء الملف التنفيذي")
        input("اضغط Enter للخروج...")
        return False

    # فحص النتيجة
    if not check_result():
        print("\n❌ فشل في إنشاء الملف التنفيذي")
        input("اضغط Enter للخروج...")
        return False

    # إنشاء حزمة التوزيع
    dist_dir = create_distribution()

    # النتيجة النهائية
    print("\n" + "🎉" + "=" * 58)
    print("   تم إنشاء الملف التنفيذي بنجاح!")
    print("=" * 60)

    print(f"\n📦 حزمة التوزيع: {dist_dir}")
    print("\n📋 محتويات الحزمة:")
    print("   📄 GESTION_DES_FORMATIONS.exe - الملف التنفيذي")
    print("   📄 تشغيل_النظام.bat - سكريبت التشغيل")
    print("   📄 اقرأني.txt - دليل الاستخدام")

    print("\n🚀 كيفية الاستخدام:")
    print("   1. انسخ مجلد التوزيع إلى الجهاز المطلوب")
    print("   2. شغل GESTION_DES_FORMATIONS.exe")
    print("   3. سيفتح النظام في المتصفح تلقائياً")

    print("\n🌐 للشبكة المحلية:")
    print("   • شغل البرنامج على الخادم الرئيسي")
    print("   • استخدم عنوان IP المعروض للوصول من أجهزة أخرى")

    print("\n✅ متوافق مع:")
    print("   • Windows 7, 8, 8.1, 10, 11")
    print("   • أنظمة 32 و 64 بت")
    print("   • عدة مستخدمين متزامنين")
    print("   • قاعدة بيانات مركزية")

    input("\nاضغط Enter للخروج...")
    return True

if __name__ == "__main__":
    main()
