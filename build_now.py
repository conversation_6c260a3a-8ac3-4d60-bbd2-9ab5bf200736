"""
بناء سريع ومباشر للملف التنفيذي
"""

import os
import sys
import subprocess
import shutil

def print_status(message):
    print(f"🔧 {message}")

def main():
    print("🚀 GESTION DES FORMATIONS - بناء سريع")
    print("=" * 50)
    
    # تنظيف المجلدات السابقة
    print_status("تنظيف المجلدات السابقة...")
    for folder in ['dist', 'build']:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"   🗑️ تم حذف {folder}")
    
    # التحقق من الملفات المطلوبة
    print_status("فحص الملفات المطلوبة...")
    required_files = ['run_exe.py', 'app/__init__.py', 'config_production.py']
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} غير موجود")
            return False
    
    # التحقق من الأيقونة
    icon_path = "app/static/images/app_icon.ico"
    if os.path.exists(icon_path):
        print(f"   ✅ الأيقونة: {icon_path}")
        icon_option = f"--icon={icon_path}"
    else:
        print(f"   ⚠️ الأيقونة غير موجودة: {icon_path}")
        icon_option = ""
    
    # بناء الملف التنفيذي باستخدام PyInstaller
    print_status("بناء الملف التنفيذي...")
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # ملف واحد
        "--windowed",                   # بدون نافذة كونسول
        "--name=GESTION_DES_FORMATIONS", # اسم الملف
        "--add-data=app;app",           # إضافة مجلد app
        "--add-data=config_production.py;.",  # إضافة ملف التكوين
        "--hidden-import=flask",
        "--hidden-import=flask_sqlalchemy",
        "--hidden-import=flask_login",
        "--hidden-import=flask_wtf",
        "--hidden-import=wtforms",
        "--hidden-import=sqlalchemy",
        "--hidden-import=werkzeug",
        "--hidden-import=jinja2",
        "--hidden-import=sqlite3",
        "--hidden-import=threading",
        "--hidden-import=webbrowser",
        "--hidden-import=socket",
        "--hidden-import=time",
        "--hidden-import=datetime",
        "--hidden-import=json",
        "--hidden-import=pathlib",
        "--hidden-import=secrets",
        "--clean",                      # تنظيف الملفات المؤقتة
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    if icon_option:
        cmd.append(icon_option)
    
    # إضافة الملف الرئيسي
    cmd.append("run_exe.py")
    
    print(f"   🚀 تشغيل: {' '.join(cmd[:5])}...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("   ✅ تم البناء بنجاح!")
        else:
            print("   ❌ فشل في البناء")
            print("   📋 رسائل الخطأ:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في عملية البناء: {e}")
        return False
    
    # فحص النتيجة
    print_status("فحص النتيجة...")
    
    exe_path = "dist/GESTION_DES_FORMATIONS.exe"
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)
        
        print(f"   🎉 الملف التنفيذي تم إنشاؤه!")
        print(f"   📍 المسار: {os.path.abspath(exe_path)}")
        print(f"   📊 الحجم: {size_mb:.1f} MB")
        
        # إنشاء مجلد التوزيع
        print_status("إنشاء مجلد التوزيع...")
        
        dist_folder = "GESTION_DES_FORMATIONS_Distribution"
        if os.path.exists(dist_folder):
            shutil.rmtree(dist_folder)
        
        os.makedirs(dist_folder)
        
        # نسخ الملف التنفيذي
        shutil.copy2(exe_path, os.path.join(dist_folder, "GESTION_DES_FORMATIONS.exe"))
        
        # إنشاء ملف تشغيل
        bat_content = '''@echo off
chcp 65001 > nul
title GESTION DES FORMATIONS - نظام إدارة التكوين

echo.
echo ========================================
echo   GESTION DES FORMATIONS
echo   نظام إدارة التكوين والتدريب
echo   ABOULFADEL.A
echo ========================================
echo.

echo 🚀 بدء تشغيل النظام...
echo.

GESTION_DES_FORMATIONS.exe

pause
'''
        
        with open(os.path.join(dist_folder, 'تشغيل_النظام.bat'), 'w', encoding='utf-8') as f:
            f.write(bat_content)
        
        # إنشاء ملف معلومات
        readme_content = '''# GESTION DES FORMATIONS
## نظام إدارة التكوين والتدريب

### معلومات النظام
- الاسم: GESTION DES FORMATIONS
- الإصدار: 1.0.0
- المطور: ABOULFADEL.A

### كيفية التشغيل
1. شغل ملف GESTION_DES_FORMATIONS.exe
2. أو شغل تشغيل_النظام.bat
3. سيفتح النظام في المتصفح تلقائياً

### للوصول من الشبكة المحلية
- شغل البرنامج على الخادم الرئيسي
- استخدم عنوان IP المعروض للوصول من أجهزة أخرى

### المستخدم الافتراضي
- اسم المستخدم: admin
- كلمة المرور: admin123
'''
        
        with open(os.path.join(dist_folder, 'اقرأني.txt'), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"   ✅ تم إنشاء مجلد التوزيع: {dist_folder}")
        
        print("\n" + "🎉" + "=" * 48)
        print("   تم إنشاء الملف التنفيذي بنجاح!")
        print("=" * 50)
        
        print(f"\n📦 مجلد التوزيع: {dist_folder}")
        print("📋 محتويات المجلد:")
        print("   📄 GESTION_DES_FORMATIONS.exe")
        print("   📄 تشغيل_النظام.bat")
        print("   📄 اقرأني.txt")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. اذهب إلى مجلد التوزيع")
        print("   2. شغل GESTION_DES_FORMATIONS.exe")
        print("   3. سيفتح النظام في المتصفح")
        
        print("\n🌐 للشبكة المحلية:")
        print("   • شغل البرنامج على الخادم الرئيسي")
        print("   • استخدم عنوان IP المعروض للوصول من أجهزة أخرى")
        
        return True
        
    else:
        print("   ❌ الملف التنفيذي غير موجود")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في إنشاء الملف التنفيذي")
    
    input("\nاضغط Enter للخروج...")
