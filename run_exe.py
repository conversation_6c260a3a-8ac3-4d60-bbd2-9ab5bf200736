"""
ملف تشغيل محسن خصيصاً للملف التنفيذي (EXE)
يحل مشاكل stdin/stdout في PyInstaller
"""

import os
import sys
import socket
import threading
import time
import webbrowser
import io
import logging

# إعداد خاص للملف التنفيذي
if getattr(sys, 'frozen', False):
    # إذا كان التطبيق مجمد (EXE)
    application_path = os.path.dirname(sys.executable)
    os.chdir(application_path)
    
    # إصلاح مشكلة stdin/stdout/stderr
    class DummyFile:
        def write(self, x): pass
        def flush(self): pass
        def read(self, x=None): return ''
        def readline(self): return ''
    
    if sys.stdin is None:
        sys.stdin = DummyFile()
    if sys.stdout is None:
        sys.stdout = DummyFile()
    if sys.stderr is None:
        sys.stderr = DummyFile()
        
    # إعداد logging للملف التنفيذي
    log_file = os.path.join(application_path, 'app.log')
    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
else:
    # إذا كان يعمل من Python العادي
    application_path = os.path.dirname(os.path.abspath(__file__))
    logging.basicConfig(level=logging.INFO)

sys.path.insert(0, application_path)

# تعيين متغيرات البيئة
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = 'False'

# إنشاء مجلد البيانات
data_dir = os.path.join(application_path, 'data')
if not os.path.exists(data_dir):
    os.makedirs(data_dir)

# تعيين مسار قاعدة البيانات
db_path = os.path.join(data_dir, 'gestion_formations.db')
os.environ['DATABASE_URL'] = f'sqlite:///{db_path}'

def safe_print(message):
    """طباعة آمنة للملف التنفيذي"""
    try:
        if getattr(sys, 'frozen', False):
            # في الملف التنفيذي، اكتب إلى ملف السجل
            logging.info(message)
        else:
            # في Python العادي، اطبع عادي
            print(message)
    except:
        pass

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception:
            return "127.0.0.1"

def check_port_available(port):
    """التحقق من توفر المنفذ"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.bind(('', port))
        s.close()
        return True
    except OSError:
        return False

def find_available_port(start_port=5000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        if check_port_available(port):
            return port
    return None

def open_browser(url):
    """فتح المتصفح"""
    time.sleep(2)
    try:
        webbrowser.open(url)
        safe_print(f"تم فتح المتصفح: {url}")
    except Exception as e:
        safe_print(f"لم يتم فتح المتصفح: {e}")

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        with app.app_context():
            from app.models import db, User
            
            # إنشاء الجداول
            db.create_all()
            safe_print("تم تهيئة قاعدة البيانات")
            
            # إنشاء مستخدم إداري إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                safe_print("تم إنشاء المستخدم الإداري: admin/admin123")
                
    except Exception as e:
        safe_print(f"خطأ في تهيئة قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    safe_print("🚀 GESTION DES FORMATIONS - بدء التشغيل")
    safe_print(f"📍 مسار التطبيق: {application_path}")
    safe_print(f"💾 قاعدة البيانات: {db_path}")
    
    try:
        # استيراد التطبيق
        from app import create_app
        global app
        app = create_app()
        safe_print("✅ تم تحميل التطبيق")
        
        # الحصول على IP والمنفذ
        local_ip = get_local_ip()
        port = find_available_port(5000)
        
        if port is None:
            safe_print("❌ لم يتم العثور على منفذ متاح")
            return
        
        safe_print(f"🌐 الخادم: http://{local_ip}:{port}")
        safe_print(f"🌐 محلي: http://localhost:{port}")
        
        # تهيئة قاعدة البيانات
        initialize_database()
        
        # فتح المتصفح
        url = f"http://{local_ip}:{port}"
        browser_thread = threading.Thread(target=open_browser, args=(url,))
        browser_thread.daemon = True
        browser_thread.start()
        
        safe_print(f"🚀 بدء الخادم على المنفذ {port}")
        
        # تشغيل الخادم مع إعدادات آمنة للملف التنفيذي
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True,
            use_reloader=False,
            passthrough_errors=False  # عدم تمرير الأخطاء لتجنب مشاكل stdout
        )
        
    except ImportError as e:
        safe_print(f"❌ خطأ في استيراد التطبيق: {e}")
    except Exception as e:
        safe_print(f"❌ خطأ عام: {e}")
    finally:
        safe_print("⏹️ تم إيقاف الخادم")

if __name__ == '__main__':
    main()
