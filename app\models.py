from app import db, login_manager
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from datetime import datetime, timezone

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), index=True, unique=True)
    email = db.Column(db.String(120), index=True, unique=True)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.Bo<PERSON>, default=False)
    nom_complet = db.Column(db.String(100))
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    derniere_connexion = db.Column(db.DateTime)

    # Permissions
    perm_fiche_inscription = db.Column(db.Boolean, default=False)
    perm_dossier_technique = db.Column(db.<PERSON>, default=False)
    perm_dossier_remboursement = db.Column(db.<PERSON>, default=False)
    perm_organisme = db.Column(db.Bo<PERSON>, default=False)
    perm_formateur = db.Column(db.Boolean, default=False)
    perm_agenda = db.Column(db.Boolean, default=False)
    perm_domaine_theme = db.Column(db.Boolean, default=False)
    perm_users = db.Column(db.Boolean, default=False)
    perm_company_info = db.Column(db.Boolean, default=False)
    perm_activity_log = db.Column(db.Boolean, default=False)
    perm_backup = db.Column(db.Boolean, default=False)
    perm_reports = db.Column(db.Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission_name):
        """Vérifie si l'utilisateur a une permission spécifique"""
        if self.is_admin:
            return True

        permission_attr = f"perm_{permission_name}"
        if hasattr(self, permission_attr):
            return bool(getattr(self, permission_attr))
        return False

@login_manager.user_loader
def load_user(id):
    return User.query.get(int(id))



# Modèle de données d'inscription (Fiche de l'inscription)
class FicheInscription(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    date_inscription = db.Column(db.DateTime, index=True, default=lambda: datetime.now(timezone.utc)) # DATE DE L'INSCRIPTION
    raison_sociale = db.Column(db.String(255)) # RAISON SOCIALE
    tel_entreprise = db.Column(db.String(50)) # TEL DE ENTREPRISE
    fax_entreprise = db.Column(db.String(50)) # FAX DE ENTREPRISE
    email = db.Column(db.String(120)) # EMAIL
    mot_de_passe_email = db.Column(db.String(128)) # MOT DE PASSE EMAIL
    patente = db.Column(db.String(100)) # PATENTE
    identifiant_fiscale = db.Column(db.String(100)) # IDENTIFIANT FISCALE
    num_rc = db.Column(db.String(100)) # N° RC
    num_cnss = db.Column(db.String(100)) # N° CNSS
    eligible = db.Column(db.Boolean, default=False) # ELIGIBLE
    ice = db.Column(db.String(100)) # ICE
    mot_de_passe_ice = db.Column(db.String(128)) # MOT DE PASSE ICE
    nombre_cadres = db.Column(db.Integer) # Nombre des CADRES
    nombre_employes = db.Column(db.Integer) # Nombre des EMPLOYES
    nombre_ouvriers = db.Column(db.Integer) # Nombre des OUVRIERS
    validation = db.Column(db.Boolean, default=False) # VALIDATION
    date_validation = db.Column(db.DateTime) # DATE DE VALIDATION
    depot_physique = db.Column(db.Boolean, default=False) # DÉPÔT PHYSIQUE
    date_depot = db.Column(db.DateTime) # DATE DE DÉPÔT
    piece_jointe = db.Column(db.String(255)) # PIÈCE JOINTE (peut stocker le chemin du fichier ici)

    # Relation avec DossierTechnique (un à un ou un à plusieurs selon la conception)
    # dossier_technique = db.relationship('DossierTechnique', backref='fiche_inscription', uselist=False)


# Modèle de dossier technique (Dossier technique)
class DossierTechnique(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    # Lien avec FicheInscription
    fiche_inscription_id = db.Column(db.Integer, db.ForeignKey('fiche_inscription.id'))
    # ENTREPRISE et Numéro de série seront récupérés depuis FicheInscription liée

    domaine_id = db.Column(db.Integer, db.ForeignKey('domaine.id')) # DOMAINES
    theme_id = db.Column(db.Integer, db.ForeignKey('theme.id')) # THÉME

    objectif = db.Column(db.Text) # Objectif (compétence visée)
    contenu_indicatif = db.Column(db.Text) # Contenu indicatif
    organisme_formation_id = db.Column(db.Integer, db.ForeignKey('organisme.id')) # Organisme de Formation
    num_cnss_organisme = db.Column(db.String(100)) # N° CNSS de l’organisme (يمكن جلبه من Organisme المرتبط)
    type_formation = db.Column(db.String(100)) # Type de formation
    cout_formation_ht = db.Column(db.Float) # Coût de la Formation HT
    effectif_global = db.Column(db.Integer) # Effectif global de la population concerné
    nombre_cadres = db.Column(db.Integer) # Nombre des Cadres
    nombre_employes = db.Column(db.Integer) # Nombre des Employes
    nombre_ouvriers = db.Column(db.Integer) # Nombre des Ouvriers
    conforme = db.Column(db.Boolean, default=False) # CONFORME
    depot_physique = db.Column(db.Boolean, default=False) # DÉPÔT PHYSIQUE (5 jours avant la formation)
    date_depot = db.Column(db.DateTime) # DATE DE DÉPÔT
    validation = db.Column(db.Boolean, default=False) # VALIDATION
    piece_jointe = db.Column(db.String(255)) # PIÈCE JOINTE

    # Relations
    fiche_inscription = db.relationship('FicheInscription', backref=db.backref('dossiers_techniques', lazy=True))
    domaine = db.relationship('Domaine', backref=db.backref('dossiers_techniques', lazy=True))
    theme = db.relationship('Theme', backref=db.backref('dossiers_techniques', lazy=True))
    organisme_formation = db.relationship('Organisme', backref=db.backref('dossiers_techniques', lazy=True))


# Modèle de dossier financier (Dossier de remboursement)
class DossierRemboursement(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    organisme_id = db.Column(db.Integer, db.ForeignKey('organisme.id')) # ORGANISME
    # Numéro de série Organisme sera récupéré depuis Organisme lié

    formateur_id = db.Column(db.Integer, db.ForeignKey('formateur.id')) # FORMATEUR
    fiche_inscription_id = db.Column(db.Integer, db.ForeignKey('fiche_inscription.id')) # ENTREPRISE (lien avec FicheInscription)
    # Numéro de série Entreprise sera récupéré depuis FicheInscription liée

    theme = db.Column(db.String(255)) # THEME (peut être lié au modèle Theme au lieu de texte)
    date = db.Column(db.DateTime) # DATE (date de remboursement? date de formation? nécessite clarification)
    contrat = db.Column(db.String(255)) # CONTRAT (numéro de contrat?)
    f2 = db.Column(db.Boolean, default=False) # F2
    f3 = db.Column(db.Boolean, default=False) # F3
    liste_de_presence = db.Column(db.Boolean, default=False) # LISTE_DE_PRÉSENCE
    fiche_synthetique_evaluation_formateur = db.Column(db.Boolean, default=False) # FICHE_SYNTHÉTIQUE_D'ÉVALUATION_DE_FORMATEUR
    f4 = db.Column(db.Boolean, default=False) # F4
    facturation = db.Column(db.String(50)) # FACTURATION (par action / globale)
    mode_de_reglement = db.Column(db.String(50)) # MODE_DE_REGLEMENT (par cheque / par virement)
    m6 = db.Column(db.Boolean, default=False) # M6
    piece_jointe = db.Column(db.String(255)) # PIECE JOINTE

    # Relations
    organisme = db.relationship('Organisme', backref=db.backref('dossiers_remboursement', lazy=True))
    formateur = db.relationship('Formateur', backref=db.backref('dossiers_remboursement', lazy=True))
    fiche_inscription = db.relationship('FicheInscription', backref=db.backref('dossiers_remboursement', lazy=True))


# Modèle de dossier administratif
class DossierAdministratif(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    fiche_inscription_id = db.Column(db.Integer, db.ForeignKey('fiche_inscription.id'))

    # Documents administratifs (cases à cocher)
    f1 = db.Column(db.Boolean, default=False)  # F1
    rc = db.Column(db.Boolean, default=False)  # RC
    statut = db.Column(db.Boolean, default=False)  # Statut
    declaration_sur_honneur = db.Column(db.Boolean, default=False)  # Déclaration sur l'honneur
    pv = db.Column(db.Boolean, default=False)  # Pv
    procuration = db.Column(db.Boolean, default=False)  # Procuration
    attestation_rib = db.Column(db.Boolean, default=False)  # Attestation de RIB

    # Métadonnées
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    date_modification = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relations
    fiche_inscription = db.relationship('FicheInscription', backref=db.backref('dossiers_administratif', lazy=True))


# Modèle d'organisme (Organisme)
class Organisme(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    raison_sociale = db.Column(db.String(255)) # Raison Sociale
    forme_juridique = db.Column(db.String(100)) # Forme juridique
    date_creation = db.Column(db.DateTime) # Date de création
    nom_prenom_gerant = db.Column(db.String(255)) # Nom et prénom du gérant
    adresse = db.Column(db.String(255)) # Adresse
    ville = db.Column(db.String(100)) # Ville
    telephone = db.Column(db.String(50)) # Téléphone
    fax = db.Column(db.String(50)) # Fax
    email = db.Column(db.String(120)) # Email
    patente = db.Column(db.String(100)) # Patente
    identifiant_fiscal = db.Column(db.String(100)) # Identifiant fiscal
    num_rc = db.Column(db.String(100)) # N° RC
    num_cnss = db.Column(db.String(100)) # N° CNSS
    piece_jointe = db.Column(db.String(255)) # Pièce jointe


# Modèle de formateur (Formateur)
class Formateur(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    nom_prenom = db.Column(db.String(255)) # Nom et prénom du FORMATEUR
    specialite = db.Column(db.String(255)) # SPECIALITE
    cv = db.Column(db.Boolean, default=False) # C.V
    diplomes = db.Column(db.Boolean, default=False) # Diplômes
    adresse = db.Column(db.String(255)) # ADRESSE
    ville = db.Column(db.String(100)) # VILLE
    num_tel = db.Column(db.String(50)) # N° DE TEL
    email = db.Column(db.String(120)) # EMAIL
    piece_jointe = db.Column(db.String(255)) # PIÈCE JOINTE

    # Relation avec AgendaFormateur
    agendas = db.relationship('AgendaFormateur', backref='formateur', lazy='dynamic')


# Modèle de domaines (DOMAINES)
class Domaine(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    nom = db.Column(db.String(255), unique=True) # Nom du DOMAINE

    # Relation avec Theme
    themes = db.relationship('Theme', backref='domaine', lazy='dynamic')


# Modèle de thèmes (THÉME)
class Theme(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    nom = db.Column(db.String(255), unique=True) # Nom du THÉME
    domaine_id = db.Column(db.Integer, db.ForeignKey('domaine.id')) # Lien avec le domaine


# Modèle d'agenda des formateurs (Agenda des formateurs)
class AgendaFormateur(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    formateur_id = db.Column(db.Integer, db.ForeignKey('formateur.id')) # Lien avec le formateur
    date_rendezvous = db.Column(db.DateTime, index=True) # Date du rendez-vous
    description = db.Column(db.Text) # Description du rendez-vous

    # Peut ajouter des contraintes pour éviter la duplication des rendez-vous pour le même formateur au même moment
    __table_args__ = (db.UniqueConstraint('formateur_id', 'date_rendezvous', name='_formateur_date_uc'),)

# Modèle de rapports (Rapport)
class Rapport(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    titre = db.Column(db.String(255), nullable=False)
    type_rapport = db.Column(db.String(50), nullable=False)  # fiche_inscription, dossier_technique, etc.
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    contenu = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relation avec l'utilisateur
    user = db.relationship('User', backref=db.backref('rapports', lazy=True))

    def __repr__(self):
        return f'<Rapport {self.titre}>'

# Modèle de notifications (Notification)
class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    message = db.Column(db.String(255), nullable=False)
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    lu = db.Column(db.Boolean, default=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relation avec l'utilisateur
    user = db.relationship('User', backref=db.backref('notifications', lazy=True))

    def __repr__(self):
        return f'<Notification {self.id}>'

# Correct Formation model definition based on app/routes.py usage
class Formation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    theme = db.Column(db.String(100), nullable=False)
    date = db.Column(db.Date, nullable=False)
    organisme_id = db.Column(db.Integer, db.ForeignKey('organisme.id'), nullable=False)
    organisme = db.relationship('Organisme', backref='formations')


# Modèle d'informations de l'entreprise (Company Information)
class CompanyInfo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom_entreprise = db.Column(db.String(255), nullable=False)  # Nom de l'entreprise
    slogan = db.Column(db.String(500))  # Slogan de l'entreprise
    adresse = db.Column(db.Text)  # Adresse
    telephone = db.Column(db.String(50))  # Téléphone
    fax = db.Column(db.String(50))  # Fax
    email = db.Column(db.String(120))  # Email
    site_web = db.Column(db.String(255))  # Site web
    logo_path = db.Column(db.String(255))  # Chemin du logo
    pied_de_page = db.Column(db.Text)  # Texte du pied de page
    couleur_principale = db.Column(db.String(7), default='#007bff')  # Couleur principale (hex)
    couleur_secondaire = db.Column(db.String(7), default='#6c757d')  # Couleur secondaire
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    date_modification = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<CompanyInfo {self.nom_entreprise}>'


# Modèle de suivi d'activité des utilisateurs (User Activity Log)
class UserActivityLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    nom_utilisateur = db.Column(db.String(100), nullable=False)  # Nom d'utilisateur
    type_action = db.Column(db.String(100), nullable=False)  # Type d'action (ajout, modification, suppression, consultation)
    module_concerne = db.Column(db.String(100), nullable=False)  # Module concerné (fiche d'inscription, dossier technique, etc.)
    description_action = db.Column(db.Text)  # Description de l'action
    adresse_ip = db.Column(db.String(45))  # Adresse IP
    user_agent = db.Column(db.String(500))  # Informations du navigateur
    date_action = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    heure_action = db.Column(db.Time, default=lambda: datetime.now(timezone.utc).time())

    # Relation avec l'utilisateur
    user = db.relationship('User', backref=db.backref('activity_logs', lazy=True))

    def __repr__(self):
        return f'<UserActivityLog {self.nom_utilisateur} - {self.type_action}>'


# Modèle de paramètres de sauvegarde (Backup Settings)
class BackupSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    backup_automatique = db.Column(db.Boolean, default=False)  # Activation de la sauvegarde automatique
    frequence_backup = db.Column(db.String(20), default='daily')  # quotidien, hebdomadaire, mensuel
    heure_backup = db.Column(db.Time, default=lambda: datetime.strptime('02:00', '%H:%M').time())  # Heure de sauvegarde
    dossier_backup = db.Column(db.String(500))  # Dossier de sauvegarde des sauvegardes
    nombre_max_backups = db.Column(db.Integer, default=10)  # Nombre de sauvegardes conservées
    compression = db.Column(db.Boolean, default=True)  # Compression des sauvegardes
    notification_email = db.Column(db.Boolean, default=False)  # Envoi de notification par email
    email_notification = db.Column(db.String(120))  # Email pour les notifications
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    date_modification = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<BackupSettings {self.frequence_backup}>'


# Modèle de journal de sauvegarde (Backup Log)
class BackupLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom_fichier = db.Column(db.String(255), nullable=False)  # Nom du fichier de sauvegarde
    chemin_fichier = db.Column(db.String(500), nullable=False)  # Chemin du fichier
    taille_fichier = db.Column(db.BigInteger)  # Taille du fichier en octets
    type_backup = db.Column(db.String(20), nullable=False)  # manual, automatic
    statut = db.Column(db.String(20), default='success')  # success, failed, in_progress
    message_erreur = db.Column(db.Text)  # Message d'erreur en cas d'échec
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # Utilisateur qui a effectué la sauvegarde (pour sauvegarde manuelle)
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    duree_backup = db.Column(db.Integer)  # Durée de sauvegarde en secondes

    # Relation avec l'utilisateur
    user = db.relationship('User', backref=db.backref('backup_logs', lazy=True))

    def __repr__(self):
        return f'<BackupLog {self.nom_fichier} - {self.statut}>'
