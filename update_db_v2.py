"""
Script pour mettre à jour la base de données avec les nouvelles fonctionnalités
"""

import sqlite3
import os

def update_database():
    """Mettre à jour toutes les bases de données"""
    
    # Chemins possibles pour la base de données
    db_paths = [
        'app.db',
        'data/gestion_formations.db',
        'GESTION_DES_FORMATIONS_Distribution/data/gestion_formations.db'
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"🔧 Mise à jour de: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 1. Ajouter F3 à dossier_remboursement si pas présent
                cursor.execute("PRAGMA table_info(dossier_remboursement)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'f3' not in columns:
                    print("  ➕ Ajout F3 à dossier_remboursement...")
                    cursor.execute("ALTER TABLE dossier_remboursement ADD COLUMN f3 BOOLEAN DEFAULT 0")
                    print("  ✅ F3 ajouté!")
                
                # 2. Créer/mettre à jour table dossier_administratif
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dossier_administratif'")
                if not cursor.fetchone():
                    print("  ➕ Création table dossier_administratif...")
                    cursor.execute("""
                        CREATE TABLE dossier_administratif (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            fiche_inscription_id INTEGER,
                            f1 BOOLEAN DEFAULT 0,
                            rc BOOLEAN DEFAULT 0,
                            statut BOOLEAN DEFAULT 0,
                            declaration_sur_honneur BOOLEAN DEFAULT 0,
                            pv BOOLEAN DEFAULT 0,
                            procuration BOOLEAN DEFAULT 0,
                            attestation_rib BOOLEAN DEFAULT 0,
                            observations TEXT,
                            piece_jointe VARCHAR(255),
                            date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
                            date_modification DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (fiche_inscription_id) REFERENCES fiche_inscription (id)
                        )
                    """)
                    print("  ✅ Table dossier_administratif créée!")
                else:
                    # Vérifier colonnes manquantes
                    cursor.execute("PRAGMA table_info(dossier_administratif)")
                    admin_columns = [column[1] for column in cursor.fetchall()]
                    
                    if 'observations' not in admin_columns:
                        print("  ➕ Ajout observations...")
                        cursor.execute("ALTER TABLE dossier_administratif ADD COLUMN observations TEXT")
                        print("  ✅ Observations ajouté!")
                    
                    if 'piece_jointe' not in admin_columns:
                        print("  ➕ Ajout piece_jointe...")
                        cursor.execute("ALTER TABLE dossier_administratif ADD COLUMN piece_jointe VARCHAR(255)")
                        print("  ✅ Piece_jointe ajouté!")
                
                # 3. Ajouter M1, F2, F3 à dossier_technique
                cursor.execute("PRAGMA table_info(dossier_technique)")
                tech_columns = [column[1] for column in cursor.fetchall()]
                
                if 'm1' not in tech_columns:
                    print("  ➕ Ajout M1 à dossier_technique...")
                    cursor.execute("ALTER TABLE dossier_technique ADD COLUMN m1 BOOLEAN DEFAULT 0")
                    print("  ✅ M1 ajouté!")
                
                if 'f2' not in tech_columns:
                    print("  ➕ Ajout F2 à dossier_technique...")
                    cursor.execute("ALTER TABLE dossier_technique ADD COLUMN f2 BOOLEAN DEFAULT 0")
                    print("  ✅ F2 ajouté!")
                
                if 'f3' not in tech_columns:
                    print("  ➕ Ajout F3 à dossier_technique...")
                    cursor.execute("ALTER TABLE dossier_technique ADD COLUMN f3 BOOLEAN DEFAULT 0")
                    print("  ✅ F3 ajouté!")
                
                conn.commit()
                conn.close()
                print(f"  🎉 {db_path} mis à jour avec succès!")
                
            except Exception as e:
                print(f"  ❌ Erreur: {e}")
                if 'conn' in locals():
                    conn.close()

if __name__ == "__main__":
    print("🚀 GESTION DES FORMATIONS - Mise à jour v2")
    print("=" * 50)
    update_database()
    print("\n✅ Mise à jour terminée!")
    print("Appuyez sur Entrée pour continuer...")
    input()
