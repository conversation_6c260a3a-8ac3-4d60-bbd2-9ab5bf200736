; ===============================================
; Inno Setup Script for GESTION DES FORMATIONS
; نظام إدارة التكوين والتدريب - ABOULFADEL.A
; يدعم جميع إصدارات Windows (7-11) و 32/64 بت
; مع دعم الشبكة المحلية وعدة مستخدمين
; ===============================================

[Setup]
; معلومات التطبيق الأساسية
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName=GESTION DES FORMATIONS
AppVersion=1.0.0
AppVerName=GESTION DES FORMATIONS 1.0.0
AppPublisher=ABOULFADEL.A
AppPublisherURL=https://www.aboulfadel.com
AppSupportURL=https://www.aboulfadel.com/support
AppUpdatesURL=https://www.aboulfadel.com/updates
AppCopyright=© 2024 ABOULFADEL.A - جميع الحقوق محفوظة
AppComments=نظام إدارة التكوين والتدريب مع دعم الشبكة المحلية

; مسارات التثبيت
DefaultDirName={autopf}\GESTION DES FORMATIONS
DefaultGroupName=GESTION DES FORMATIONS
AllowNoIcons=yes
DisableDirPage=no
DisableProgramGroupPage=no
CreateAppDir=yes

; ملف الإخراج والضغط
OutputDir=Output
OutputBaseFilename=GESTION_DES_FORMATIONS_Setup_v1.0.0
SetupIconFile=app\static\images\app_icon.ico
UninstallDisplayIcon={app}\GESTION_DES_FORMATIONS.exe
Compression=lzma2/ultra64
SolidCompression=yes
InternalCompressLevel=ultra64
WizardStyle=modern

; متطلبات النظام المحسنة
MinVersion=6.1sp1
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

; إعدادات المثبت المحسنة
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog
DisableProgramGroupPage=no
UsePreviousAppDir=yes
UsePreviousGroup=yes
UsePreviousSetupType=yes
UsePreviousTasks=yes
AlwaysRestart=no
RestartIfNeededByRun=no

; إعدادات واجهة المثبت
WizardImageFile=
WizardSmallImageFile=
WizardImageStretch=yes
WizardImageBackColor=clWhite
DisableWelcomePage=no
DisableFinishedPage=no

; إعدادات اللغة
ShowLanguageDialog=auto
LanguageDetectionMethod=locale

[Languages]
Name: "arabic"; MessagesFile: "compiler:Default.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Tasks]
Name: "desktopicon"; Description: "إنشاء أيقونة على سطح المكتب"; GroupDescription: "أيقونات إضافية"; Flags: checked
Name: "quicklaunchicon"; Description: "إنشاء أيقونة في شريط المهام السريع"; GroupDescription: "أيقونات إضافية"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "إضافة إلى قائمة ابدأ"; GroupDescription: "أيقونات إضافية"; Flags: checked
Name: "firewall"; Description: "إضافة استثناء في جدار الحماية Windows (مطلوب للشبكة المحلية)"; GroupDescription: "إعدادات الشبكة"; Flags: checked
Name: "autostart"; Description: "تشغيل تلقائي مع Windows (للخادم المحلي)"; GroupDescription: "إعدادات الشبكة"; Flags: unchecked
Name: "associate"; Description: "ربط ملفات قاعدة البيانات (.db) بالبرنامج"; GroupDescription: "إعدادات إضافية"; Flags: unchecked

[Files]
; الملف التنفيذي الرئيسي
Source: "GESTION_DES_FORMATIONS_Distribution\GESTION_DES_FORMATIONS.exe"; DestDir: "{app}"; Flags: ignoreversion signonce

; ملفات مساعدة ودليل الاستخدام
Source: "GESTION_DES_FORMATIONS_Distribution\تشغيل_النظام.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "GESTION_DES_FORMATIONS_Distribution\اقرأني.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "دليل_البناء_والتثبيت.md"; DestDir: "{app}\docs"; Flags: ignoreversion skipifsourcedoesntexist

; الشعار والأيقونات
Source: "app\static\images\Formation-continue-1024x1024.png"; DestDir: "{app}\images"; Flags: ignoreversion
Source: "app\static\images\app_icon.ico"; DestDir: "{app}\images"; Flags: ignoreversion
Source: "app\static\images\favicon.ico"; DestDir: "{app}\images"; Flags: ignoreversion skipifsourcedoesntexist

; ملفات التكوين والدعم
Source: "config_production.py"; DestDir: "{app}\config"; Flags: ignoreversion skipifsourcedoesntexist
Source: "requirements.txt"; DestDir: "{app}\docs"; Flags: ignoreversion skipifsourcedoesntexist

; ملفات اختبار وتشخيص
Source: "test_compatibility.py"; DestDir: "{app}\tools"; Flags: ignoreversion skipifsourcedoesntexist
Source: "اختبار_الملف_التنفيذي.bat"; DestDir: "{app}\tools"; Flags: ignoreversion skipifsourcedoesntexist

; ملفات إضافية للمطورين
Source: "build_now.py"; DestDir: "{app}\tools"; Flags: ignoreversion skipifsourcedoesntexist
Source: "create_icon.py"; DestDir: "{app}\tools"; Flags: ignoreversion skipifsourcedoesntexist

[Icons]
; أيقونات قائمة البرامج
Name: "{group}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Comment: "نظام إدارة التكوين والتدريب"
Name: "{group}\تشغيل النظام مع معلومات الشبكة"; Filename: "{app}\تشغيل_النظام.bat"; IconFilename: "{app}\images\app_icon.ico"; Comment: "تشغيل مع عرض معلومات الشبكة المحلية"
Name: "{group}\دليل الاستخدام"; Filename: "{app}\اقرأني.txt"; Comment: "دليل شامل لاستخدام النظام"
Name: "{group}\أدوات التشخيص"; Filename: "{app}\tools"; IconFilename: "{app}\images\app_icon.ico"; Comment: "أدوات الاختبار والتشخيص"
Name: "{group}\مجلد البيانات"; Filename: "{app}\data"; Comment: "مجلد قاعدة البيانات والملفات"
Name: "{group}\{cm:UninstallProgram,GESTION DES FORMATIONS}"; Filename: "{uninstallexe}"; Comment: "إزالة البرنامج"

; أيقونة سطح المكتب
Name: "{autodesktop}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Tasks: desktopicon; Comment: "نظام إدارة التكوين والتدريب"

; أيقونة شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Tasks: quicklaunchicon

; أيقونة قائمة ابدأ
Name: "{userstartmenu}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Tasks: startmenu

[Registry]
; تسجيل التطبيق في النظام
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "Publisher"; ValueData: "ABOULFADEL.A"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "DisplayName"; ValueData: "GESTION DES FORMATIONS"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "UninstallString"; ValueData: "{uninstallexe}"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "InstallDate"; ValueData: "{code:GetInstallDate}"

; ربط ملفات قاعدة البيانات (اختياري)
Root: HKCR; Subkey: ".gdf"; ValueType: string; ValueName: ""; ValueData: "GestionFormationsDB"; Flags: uninsdeletevalue; Tasks: associate
Root: HKCR; Subkey: "GestionFormationsDB"; ValueType: string; ValueName: ""; ValueData: "GESTION DES FORMATIONS Database"; Flags: uninsdeletekey; Tasks: associate
Root: HKCR; Subkey: "GestionFormationsDB\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\images\app_icon.ico"; Tasks: associate
Root: HKCR; Subkey: "GestionFormationsDB\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\GESTION_DES_FORMATIONS.exe"" ""%1"""; Tasks: associate

; إعدادات التشغيل التلقائي (اختياري)
Root: HKCU; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "GESTION DES FORMATIONS"; ValueData: """{app}\GESTION_DES_FORMATIONS.exe"""; Flags: uninsdeletevalue; Tasks: autostart

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\GESTION_DES_FORMATIONS.exe"; Description: "تشغيل GESTION DES FORMATIONS الآن"; Flags: nowait postinstall skipifsilent unchecked

; فتح دليل الاستخدام
Filename: "{app}\اقرأني.txt"; Description: "فتح دليل الاستخدام"; Flags: postinstall skipifsilent unchecked shellexec

; إضافة استثناءات جدار الحماية للشبكة المحلية
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS Application"" dir=in action=allow program=""{app}\GESTION_DES_FORMATIONS.exe"" enable=yes"; Flags: runhidden; Tasks: firewall
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS Port 5000"" dir=in action=allow protocol=TCP localport=5000 enable=yes"; Flags: runhidden; Tasks: firewall
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS Port Range"" dir=in action=allow protocol=TCP localport=5000-5010 enable=yes"; Flags: runhidden; Tasks: firewall

; إنشاء مجلدات البيانات
Filename: "{sys}\cmd.exe"; Parameters: "/c mkdir ""{app}\data"" 2>nul"; Flags: runhidden
Filename: "{sys}\cmd.exe"; Parameters: "/c mkdir ""{app}\backups"" 2>nul"; Flags: runhidden
Filename: "{sys}\cmd.exe"; Parameters: "/c mkdir ""{app}\logs"" 2>nul"; Flags: runhidden
Filename: "{sys}\cmd.exe"; Parameters: "/c mkdir ""{app}\uploads"" 2>nul"; Flags: runhidden

[UninstallRun]
; إزالة قواعد جدار الحماية عند إلغاء التثبيت
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS Application"""; Flags: runhidden
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS Port 5000"""; Flags: runhidden
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS Port Range"""; Flags: runhidden

; إيقاف العمليات المتعلقة بالبرنامج
Filename: "taskkill"; Parameters: "/f /im GESTION_DES_FORMATIONS.exe"; Flags: runhidden

[Dirs]
; إنشاء مجلدات البيانات مع صلاحيات كاملة
Name: "{app}"; Permissions: users-full
Name: "{app}\data"; Permissions: users-full
Name: "{app}\backups"; Permissions: users-full
Name: "{app}\logs"; Permissions: users-full
Name: "{app}\uploads"; Permissions: users-full
Name: "{app}\images"; Permissions: users-full
Name: "{app}\config"; Permissions: users-full
Name: "{app}\docs"; Permissions: users-full
Name: "{app}\tools"; Permissions: users-full

; مجلدات مشتركة للشبكة المحلية
Name: "{commonappdata}\GESTION DES FORMATIONS"; Permissions: users-full
Name: "{commonappdata}\GESTION DES FORMATIONS\shared"; Permissions: users-full

[Code]
// ===============================================
// كود Pascal المخصص للمثبت
// ===============================================

var
  NetworkInfoPage: TInputQueryWizardPage;
  ServerModeCheckBox: TNewCheckBox;

// الحصول على تاريخ التثبيت
function GetInstallDate(Param: String): String;
begin
  Result := GetDateTimeString('yyyy-mm-dd hh:nn:ss', #0, #0);
end;

// فحص إصدار Windows المحسن
function IsWindowsVersionSupported: Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  // Windows 7 SP1 أو أحدث (Build 7601+)
  if Version.Major > 6 then
    Result := True
  else if (Version.Major = 6) and (Version.Minor > 1) then
    Result := True
  else if (Version.Major = 6) and (Version.Minor = 1) and (Version.ServicePackMajor >= 1) then
    Result := True
  else
    Result := False;
end;

// فحص المساحة المطلوبة (100 MB)
function CheckDiskSpace: Boolean;
var
  FreeSpace: Cardinal;
begin
  Result := True;
  if GetSpaceOnDisk(ExtractFileDrive(WizardDirValue), False, FreeSpace, FreeSpace, FreeSpace) then
  begin
    if FreeSpace < (100 * 1024 * 1024) then // 100 MB
    begin
      MsgBox('المساحة المتاحة غير كافية. يتطلب البرنامج 100 MB على الأقل.', mbError, MB_OK);
      Result := False;
    end;
  end;
end;

// فحص صلاحيات المدير
function IsAdminInstallMode: Boolean;
begin
  Result := IsAdminLoggedOn or IsPowerUserLoggedOn;
end;

// إنشاء صفحة إعدادات الشبكة
procedure CreateNetworkConfigPage;
begin
  NetworkInfoPage := CreateInputQueryPage(wpSelectTasks,
    'إعدادات الشبكة المحلية',
    'تكوين البرنامج للعمل في الشبكة المحلية',
    'يمكنك تكوين البرنامج ليعمل كخادم في الشبكة المحلية، مما يسمح للمستخدمين الآخرين بالوصول إليه من أجهزة مختلفة.');

  NetworkInfoPage.Add('اسم الخادم (اختياري):', False);
  NetworkInfoPage.Add('وصف الخادم (اختياري):', False);

  NetworkInfoPage.Values[0] := GetComputerNameString;
  NetworkInfoPage.Values[1] := 'خادم GESTION DES FORMATIONS';
end;

// تهيئة المثبت
function InitializeSetup: Boolean;
var
  ResultCode: Integer;
begin
  Result := True;

  // فحص إصدار Windows
  if not IsWindowsVersionSupported then
  begin
    MsgBox('هذا البرنامج يتطلب Windows 7 SP1 أو إصدار أحدث.' + #13#10 +
           'إصدار Windows الحالي غير مدعوم.', mbError, MB_OK);
    Result := False;
    Exit;
  end;

  // فحص المساحة
  if not CheckDiskSpace then
  begin
    Result := False;
    Exit;
  end;

  // رسالة ترحيب
  if MsgBox('مرحباً بك في مثبت GESTION DES FORMATIONS' + #13#10 +
           'نظام إدارة التكوين والتدريب مع دعم الشبكة المحلية' + #13#10 + #13#10 +
           'المميزات:' + #13#10 +
           '• يدعم جميع إصدارات Windows (7-11)' + #13#10 +
           '• يدعم الشبكة المحلية وعدة مستخدمين' + #13#10 +
           '• قاعدة بيانات مركزية' + #13#10 +
           '• لا يحتاج برامج إضافية' + #13#10 + #13#10 +
           'هل تريد المتابعة؟', mbConfirmation, MB_YESNO) = IDNO then
  begin
    Result := False;
  end;
end;

// تهيئة معالج التثبيت
procedure InitializeWizard;
begin
  CreateNetworkConfigPage;
end;

// معالجة خطوات التثبيت
procedure CurStepChanged(CurStep: TSetupStep);
var
  ConfigContent: String;
  ServerName, ServerDesc: String;
begin
  if CurStep = ssPostInstall then
  begin
    // الحصول على إعدادات الشبكة
    ServerName := NetworkInfoPage.Values[0];
    ServerDesc := NetworkInfoPage.Values[1];

    if ServerName = '' then
      ServerName := GetComputerNameString;
    if ServerDesc = '' then
      ServerDesc := 'خادم GESTION DES FORMATIONS';

    // إنشاء ملف تكوين شامل
    ConfigContent := '[GESTION_DES_FORMATIONS]' + #13#10 +
                    'Version=1.0.0' + #13#10 +
                    'InstallDate=' + GetInstallDate('') + #13#10 +
                    'Publisher=ABOULFADEL.A' + #13#10 +
                    'InstallPath=' + ExpandConstant('{app}') + #13#10 +
                    'ServerName=' + ServerName + #13#10 +
                    'ServerDescription=' + ServerDesc + #13#10 +
                    'NetworkEnabled=true' + #13#10 +
                    'DefaultPort=5000' + #13#10 +
                    'DatabasePath=data\gestion_formations.db' + #13#10 +
                    'BackupPath=backups' + #13#10 +
                    'LogPath=logs' + #13#10 +
                    'UploadPath=uploads' + #13#10;

    // حفظ ملف التكوين
    SaveStringToFile(ExpandConstant('{app}\data\config.ini'), ConfigContent, False);

    // إنشاء ملف معلومات النظام
    ConfigContent := 'GESTION DES FORMATIONS - System Information' + #13#10 +
                    '============================================' + #13#10 +
                    'Install Date: ' + GetInstallDate('') + #13#10 +
                    'Install Path: ' + ExpandConstant('{app}') + #13#10 +
                    'Windows Version: ' + GetWindowsVersionString + #13#10 +
                    'Server Name: ' + ServerName + #13#10 +
                    'Server Description: ' + ServerDesc + #13#10 +
                    '============================================' + #13#10;

    SaveStringToFile(ExpandConstant('{app}\system_info.txt'), ConfigContent, False);
  end;
end;

// معالجة تغيير الصفحات
procedure CurPageChanged(CurPageID: Integer);
begin
  // صفحة الترحيب
  if CurPageID = wpWelcome then
  begin
    WizardForm.WelcomeLabel2.Caption :=
      'سيقوم هذا المعالج بتثبيت GESTION DES FORMATIONS على جهازك.' + #13#10 + #13#10 +
      'نظام إدارة التكوين والتدريب مع دعم الشبكة المحلية' + #13#10 + #13#10 +
      'المميزات الرئيسية:' + #13#10 +
      '• دعم جميع إصدارات Windows (7, 8, 8.1, 10, 11)' + #13#10 +
      '• دعم أنظمة 32 و 64 بت' + #13#10 +
      '• دعم الشبكة المحلية وعدة مستخدمين' + #13#10 +
      '• قاعدة بيانات مركزية' + #13#10 +
      '• لا يحتاج برامج إضافية' + #13#10 + #13#10 +
      'انقر "التالي" للمتابعة أو "إلغاء" للخروج.';
  end;

  // صفحة النهاية
  if CurPageID = wpFinished then
  begin
    WizardForm.FinishedLabel.Caption :=
      'تم تثبيت GESTION DES FORMATIONS بنجاح!' + #13#10 + #13#10 +
      'يمكنك الآن تشغيل البرنامج من:' + #13#10 +
      '• قائمة البرامج: GESTION DES FORMATIONS' + #13#10 +
      '• سطح المكتب (إذا اخترت إنشاء أيقونة)' + #13#10 +
      '• مجلد التثبيت: ' + ExpandConstant('{app}') + #13#10 + #13#10 +
      'للوصول من الشبكة المحلية:' + #13#10 +
      '1. شغل البرنامج على هذا الجهاز (الخادم)' + #13#10 +
      '2. استخدم عنوان IP المعروض للوصول من أجهزة أخرى' + #13#10 +
      '3. تأكد من فتح المنفذ 5000 في جدار الحماية' + #13#10 + #13#10 +
      'المستخدم الافتراضي: admin / admin123';
  end;
end;

// فحص قبل إلغاء التثبيت
function InitializeUninstall(): Boolean;
begin
  Result := True;
  if MsgBox('هل أنت متأكد من إزالة GESTION DES FORMATIONS؟' + #13#10 + #13#10 +
           'سيتم حذف البرنامج ولكن ستبقى ملفات البيانات محفوظة.' + #13#10 +
           'يمكنك حذفها يدوياً لاحقاً إذا أردت.',
           mbConfirmation, MB_YESNO) = IDNO then
  begin
    Result := False;
  end;
end;
