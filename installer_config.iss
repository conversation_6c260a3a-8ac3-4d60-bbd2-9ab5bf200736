; Inno Setup Script for GESTION DES FORMATIONS
; نظام إدارة التكوين والتدريب - ABOULFADEL.A
; يدعم جميع إصدارات Windows (7-11) و 32/64 بت

[Setup]
; معلومات التطبيق الأساسية
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName=GESTION DES FORMATIONS
AppVersion=1.0.0
AppVerName=GESTION DES FORMATIONS 1.0.0
AppPublisher=ABOULFADEL.A
AppPublisherURL=
AppSupportURL=
AppUpdatesURL=
AppCopyright=© 2024 ABOULFADEL.A

; مسارات التثبيت
DefaultDirName={autopf}\GESTION DES FORMATIONS
DefaultGroupName=GESTION DES FORMATIONS
AllowNoIcons=yes

; ملف الإخراج
OutputDir=Output
OutputBaseFilename=GESTION_DES_FORMATIONS_Setup
SetupIconFile=app\static\images\app_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=6.1sp1
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

; إعدادات المثبت
PrivilegesRequired=admin
DisableProgramGroupPage=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
WizardImageFile=
WizardSmallImageFile=

; إعدادات اللغة والترميز
ShowLanguageDialog=no
LanguageDetectionMethod=none

[Languages]
Name: "arabic"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "firewall"; Description: "إضافة استثناء في جدار الحماية Windows"; GroupDescription: "إعدادات الشبكة"; Flags: unchecked

[Files]
; الملف التنفيذي الرئيسي
Source: "GESTION_DES_FORMATIONS_Distribution\GESTION_DES_FORMATIONS.exe"; DestDir: "{app}"; Flags: ignoreversion

; ملفات مساعدة
Source: "GESTION_DES_FORMATIONS_Distribution\تشغيل_النظام.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "GESTION_DES_FORMATIONS_Distribution\اقرأني.txt"; DestDir: "{app}"; Flags: ignoreversion

; الشعار والأيقونات
Source: "app\static\images\Formation-continue-1024x1024.png"; DestDir: "{app}\images"; Flags: ignoreversion
Source: "app\static\images\app_icon.ico"; DestDir: "{app}\images"; Flags: ignoreversion

; ملفات إضافية إذا كانت موجودة
Source: "app\static\images\favicon.ico"; DestDir: "{app}\images"; Flags: ignoreversion skipifsourcedoesntexist

[Icons]
; أيقونة في قائمة البرامج
Name: "{group}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"
Name: "{group}\تشغيل النظام"; Filename: "{app}\تشغيل_النظام.bat"; IconFilename: "{app}\images\app_icon.ico"
Name: "{group}\دليل الاستخدام"; Filename: "{app}\اقرأني.txt"
Name: "{group}\{cm:UninstallProgram,GESTION DES FORMATIONS}"; Filename: "{uninstallexe}"

; أيقونة على سطح المكتب
Name: "{autodesktop}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Tasks: desktopicon

; أيقونة في شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Tasks: quicklaunchicon

[Registry]
; تسجيل التطبيق في النظام
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "Publisher"; ValueData: "ABOULFADEL.A"

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\GESTION_DES_FORMATIONS.exe"; Description: "{cm:LaunchProgram,GESTION DES FORMATIONS}"; Flags: nowait postinstall skipifsilent

; إضافة استثناء في جدار الحماية
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS"" dir=in action=allow program=""{app}\GESTION_DES_FORMATIONS.exe"""; Flags: runhidden; Tasks: firewall
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS Port"" dir=in action=allow protocol=TCP localport=5000"; Flags: runhidden; Tasks: firewall

[UninstallRun]
; إزالة قواعد جدار الحماية عند إلغاء التثبيت
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS"""; Flags: runhidden
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS Port"""; Flags: runhidden

[Dirs]
; إنشاء مجلدات البيانات
Name: "{app}\data"; Permissions: users-full
Name: "{app}\backups"; Permissions: users-full
Name: "{app}\logs"; Permissions: users-full
Name: "{app}\uploads"; Permissions: users-full

[Code]
// كود Pascal للمثبت

// فحص إصدار Windows
function IsWindowsVersionSupported: Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  // Windows 7 SP1 أو أحدث
  Result := (Version.Major > 6) or 
           ((Version.Major = 6) and (Version.Minor >= 1) and (Version.ServicePackMajor >= 1));
end;

// فحص .NET Framework (إذا كان مطلوباً)
function IsDotNetInstalled: Boolean;
begin
  Result := True; // لا نحتاج .NET لهذا التطبيق
end;

// فحص المساحة المطلوبة
function CheckDiskSpace: Boolean;
begin
  Result := True; // سيتم فحص المساحة تلقائياً
end;

// رسالة ترحيب مخصصة
function InitializeSetup: Boolean;
begin
  Result := True;
  
  if not IsWindowsVersionSupported then
  begin
    MsgBox('هذا البرنامج يتطلب Windows 7 SP1 أو إصدار أحدث.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  // رسالة ترحيب
  if MsgBox('مرحباً بك في مثبت GESTION DES FORMATIONS' + #13#10 + 
           'نظام إدارة التكوين والتدريب' + #13#10 + #13#10 +
           'هل تريد المتابعة؟', mbConfirmation, MB_YESNO) = IDNO then
  begin
    Result := False;
  end;
end;

// بعد التثبيت
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء ملف تكوين أولي
    SaveStringToFile(ExpandConstant('{app}\data\config.ini'), 
                    '[GESTION_DES_FORMATIONS]' + #13#10 +
                    'Version=1.0.0' + #13#10 +
                    'InstallDate=' + GetDateTimeString('yyyy-mm-dd hh:nn:ss', #0, #0) + #13#10 +
                    'Publisher=ABOULFADEL.A' + #13#10, False);
  end;
end;

// رسالة النهاية
procedure CurPageChanged(CurPageID: Integer);
begin
  if CurPageID = wpFinished then
  begin
    WizardForm.FinishedLabel.Caption := 
      'تم تثبيت GESTION DES FORMATIONS بنجاح!' + #13#10 + #13#10 +
      'يمكنك الآن تشغيل البرنامج من:' + #13#10 +
      '• قائمة البرامج' + #13#10 +
      '• سطح المكتب (إذا اخترت إنشاء أيقونة)' + #13#10 +
      '• مجلد التثبيت مباشرة' + #13#10 + #13#10 +
      'للوصول من الشبكة المحلية، شغل البرنامج واستخدم عنوان IP المعروض.';
  end;
end;
