('C:\\Users\\<USER>\\Desktop\\Getion des formation '
 'jadid\\build\\GESTION_DES_FORMATIONS\\PYZ-00.pyz',
 [('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('app',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\__init__.py',
   'PYMODULE'),
  ('app.forms',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\forms.py',
   'PYMODULE'),
  ('app.models',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\models.py',
   'PYMODULE'),
  ('app.routes',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\routes.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cgi.py',
   'PYMODULE'),
  ('click',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\config.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('dns',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\_asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\_asyncio_backend.py',
   'PYMODULE'),
  ('dns._ddr',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\_ddr.py',
   'PYMODULE'),
  ('dns._features',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\_features.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\_immutable_ctx.py',
   'PYMODULE'),
  ('dns._trio_backend',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\_trio_backend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncquery',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\asyncquery.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\asyncresolver.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\dnssectypes.py',
   'PYMODULE'),
  ('dns.edns',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\edns.py',
   'PYMODULE'),
  ('dns.entropy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.enum',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\enum.py',
   'PYMODULE'),
  ('dns.exception',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('dns.flags',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\flags.py',
   'PYMODULE'),
  ('dns.grange',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\grange.py',
   'PYMODULE'),
  ('dns.immutable',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\immutable.py',
   'PYMODULE'),
  ('dns.inet',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\inet.py',
   'PYMODULE'),
  ('dns.ipv4',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\ipv4.py',
   'PYMODULE'),
  ('dns.ipv6',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\ipv6.py',
   'PYMODULE'),
  ('dns.message',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.name',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\name.py',
   'PYMODULE'),
  ('dns.nameserver',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\nameserver.py',
   'PYMODULE'),
  ('dns.node',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\node.py',
   'PYMODULE'),
  ('dns.opcode',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.query',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\query.py',
   'PYMODULE'),
  ('dns.quic',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\quic\\__init__.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\quic\\_asyncio.py',
   'PYMODULE'),
  ('dns.quic._common',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\quic\\_common.py',
   'PYMODULE'),
  ('dns.quic._sync',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\quic\\_sync.py',
   'PYMODULE'),
  ('dns.quic._trio',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\quic\\_trio.py',
   'PYMODULE'),
  ('dns.rcode',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rcode.py',
   'PYMODULE'),
  ('dns.rdata',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdata.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rdataset',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.rdatatype',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.renderer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.resolver',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns.reversename',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rrset',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\rrset.py',
   'PYMODULE'),
  ('dns.serial',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.set',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\set.py',
   'PYMODULE'),
  ('dns.tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.transaction',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\transaction.py',
   'PYMODULE'),
  ('dns.tsig',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\tsig.py',
   'PYMODULE'),
  ('dns.ttl',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\ttl.py',
   'PYMODULE'),
  ('dns.update',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('dns.win32util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\win32util.py',
   'PYMODULE'),
  ('dns.wire',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\wire.py',
   'PYMODULE'),
  ('dns.xfr',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\xfr.py',
   'PYMODULE'),
  ('dns.zone',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\zone.py',
   'PYMODULE'),
  ('dns.zonefile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\zonefile.py',
   'PYMODULE'),
  ('dns.zonetypes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\dns\\zonetypes.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email_validator',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\email_validator\\__init__.py',
   'PYMODULE'),
  ('email_validator.deliverability',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\email_validator\\deliverability.py',
   'PYMODULE'),
  ('email_validator.exceptions_types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\email_validator\\exceptions_types.py',
   'PYMODULE'),
  ('email_validator.rfc_constants',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\email_validator\\rfc_constants.py',
   'PYMODULE'),
  ('email_validator.syntax',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\email_validator\\syntax.py',
   'PYMODULE'),
  ('email_validator.validate_email',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\email_validator\\validate_email.py',
   'PYMODULE'),
  ('flask',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_login',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\__init__.py',
   'PYMODULE'),
  ('flask_login.__about__',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\__about__.py',
   'PYMODULE'),
  ('flask_login.config',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\config.py',
   'PYMODULE'),
  ('flask_login.login_manager',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\login_manager.py',
   'PYMODULE'),
  ('flask_login.mixins',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\mixins.py',
   'PYMODULE'),
  ('flask_login.signals',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\signals.py',
   'PYMODULE'),
  ('flask_login.test_client',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\test_client.py',
   'PYMODULE'),
  ('flask_login.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_login\\utils.py',
   'PYMODULE'),
  ('flask_sqlalchemy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('flask_sqlalchemy.cli',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\cli.py',
   'PYMODULE'),
  ('flask_sqlalchemy.extension',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\extension.py',
   'PYMODULE'),
  ('flask_sqlalchemy.model',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\model.py',
   'PYMODULE'),
  ('flask_sqlalchemy.pagination',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\pagination.py',
   'PYMODULE'),
  ('flask_sqlalchemy.query',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\query.py',
   'PYMODULE'),
  ('flask_sqlalchemy.record_queries',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\record_queries.py',
   'PYMODULE'),
  ('flask_sqlalchemy.session',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\session.py',
   'PYMODULE'),
  ('flask_sqlalchemy.track_modifications',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\track_modifications.py',
   'PYMODULE'),
  ('flask_wtf',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\__init__.py',
   'PYMODULE'),
  ('flask_wtf.csrf',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\csrf.py',
   'PYMODULE'),
  ('flask_wtf.file',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\file.py',
   'PYMODULE'),
  ('flask_wtf.form',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\form.py',
   'PYMODULE'),
  ('flask_wtf.i18n',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\i18n.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\__init__.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.fields',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\fields.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.validators',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\validators.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.widgets',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\widgets.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\glob.py',
   'PYMODULE'),
  ('greenlet',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('itsdangerous',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\opcode.py',
   'PYMODULE'),
  ('packaging',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\random.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\datastructures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wtforms',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\csrf\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf.core',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\csrf\\core.py',
   'PYMODULE'),
  ('wtforms.csrf.session',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\csrf\\session.py',
   'PYMODULE'),
  ('wtforms.fields',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\__init__.py',
   'PYMODULE'),
  ('wtforms.fields.choices',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\choices.py',
   'PYMODULE'),
  ('wtforms.fields.core',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\core.py',
   'PYMODULE'),
  ('wtforms.fields.datetime',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\datetime.py',
   'PYMODULE'),
  ('wtforms.fields.form',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\form.py',
   'PYMODULE'),
  ('wtforms.fields.list',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\list.py',
   'PYMODULE'),
  ('wtforms.fields.numeric',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\numeric.py',
   'PYMODULE'),
  ('wtforms.fields.simple',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\fields\\simple.py',
   'PYMODULE'),
  ('wtforms.form',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\form.py',
   'PYMODULE'),
  ('wtforms.i18n',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\i18n.py',
   'PYMODULE'),
  ('wtforms.meta',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\meta.py',
   'PYMODULE'),
  ('wtforms.utils',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\utils.py',
   'PYMODULE'),
  ('wtforms.validators',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\validators.py',
   'PYMODULE'),
  ('wtforms.widgets',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\widgets\\__init__.py',
   'PYMODULE'),
  ('wtforms.widgets.core',
   'c:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\widgets\\core.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipimport.py',
   'PYMODULE')])
