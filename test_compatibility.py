"""
سكريبت اختبار التوافق - GESTION DES FORMATIONS
فحص التوافق مع جميع إصدارات Windows المطلوبة
"""

import os
import sys
import platform
import socket
import subprocess
import time
import threading
from pathlib import Path

def print_header():
    """طباعة رأس الاختبار"""
    print("🧪 " + "=" * 58)
    print("   GESTION DES FORMATIONS - اختبار التوافق")
    print("   فحص التوافق مع جميع إصدارات Windows")
    print("   ABOULFADEL.A")
    print("=" * 60)
    print()

def get_windows_version():
    """الحصول على معلومات إصدار Windows"""
    try:
        import winreg
        
        # قراءة معلومات Windows من الريجستري
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SOFTWARE\Microsoft\Windows NT\CurrentVersion")
        
        product_name = winreg.QueryValueEx(key, "ProductName")[0]
        current_build = winreg.QueryValueEx(key, "CurrentBuild")[0]
        
        try:
            display_version = winreg.QueryValueEx(key, "DisplayVersion")[0]
        except FileNotFoundError:
            display_version = "غير متاح"
        
        winreg.CloseKey(key)
        
        return {
            'name': product_name,
            'build': current_build,
            'version': display_version,
            'platform': platform.platform(),
            'architecture': platform.architecture()[0]
        }
        
    except Exception as e:
        return {
            'name': platform.system(),
            'build': 'غير معروف',
            'version': platform.version(),
            'platform': platform.platform(),
            'architecture': platform.architecture()[0],
            'error': str(e)
        }

def check_windows_compatibility():
    """فحص توافق إصدار Windows"""
    print("🔍 فحص إصدار Windows...")
    
    win_info = get_windows_version()
    
    print(f"📋 معلومات النظام:")
    print(f"   🖥️  النظام: {win_info['name']}")
    print(f"   🏗️  البناء: {win_info['build']}")
    print(f"   📊 الإصدار: {win_info['version']}")
    print(f"   🏛️  المعمارية: {win_info['architecture']}")
    print(f"   🔧 المنصة: {win_info['platform']}")
    
    # فحص التوافق
    compatible = True
    
    # فحص إصدار Windows
    try:
        build_num = int(win_info['build'])
        
        if build_num >= 22000:  # Windows 11
            print("✅ Windows 11 - متوافق")
        elif build_num >= 10240:  # Windows 10
            print("✅ Windows 10 - متوافق")
        elif build_num >= 9600:  # Windows 8.1
            print("✅ Windows 8.1 - متوافق")
        elif build_num >= 9200:  # Windows 8
            print("✅ Windows 8 - متوافق")
        elif build_num >= 7601:  # Windows 7 SP1
            print("✅ Windows 7 SP1 - متوافق")
        elif build_num >= 7600:  # Windows 7
            print("⚠️ Windows 7 - يُنصح بتحديث إلى SP1")
        else:
            print("❌ إصدار Windows غير مدعوم")
            compatible = False
            
    except ValueError:
        print("⚠️ لا يمكن تحديد إصدار Windows بدقة")
    
    # فحص المعمارية
    if '64' in win_info['architecture']:
        print("✅ نظام 64 بت - مدعوم")
    elif '32' in win_info['architecture']:
        print("✅ نظام 32 بت - مدعوم")
    else:
        print("⚠️ معمارية غير معروفة")
    
    return compatible

def check_python_compatibility():
    """فحص توافق Python"""
    print("\n🐍 فحص Python...")
    
    version = sys.version_info
    print(f"📊 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 7):
        print("✅ إصدار Python متوافق")
        return True
    else:
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False

def check_network_capabilities():
    """فحص قدرات الشبكة"""
    print("\n🌐 فحص قدرات الشبكة...")
    
    try:
        # فحص الحصول على IP المحلي
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"✅ اسم الجهاز: {hostname}")
        print(f"✅ عنوان IP المحلي: {local_ip}")
        
        # فحص توفر المنافذ
        ports_to_check = [5000, 5001, 5002]
        available_ports = []
        
        for port in ports_to_check:
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.bind(('', port))
                s.close()
                available_ports.append(port)
                print(f"✅ المنفذ {port} متاح")
            except OSError:
                print(f"⚠️ المنفذ {port} مستخدم")
        
        if available_ports:
            print(f"✅ منافذ متاحة: {available_ports}")
            return True
        else:
            print("⚠️ لا توجد منافذ متاحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الشبكة: {e}")
        return False

def check_file_permissions():
    """فحص صلاحيات الملفات"""
    print("\n📁 فحص صلاحيات الملفات...")
    
    try:
        # فحص صلاحية الكتابة في المجلد الحالي
        test_file = "test_permissions.tmp"
        
        with open(test_file, 'w') as f:
            f.write("test")
        
        os.remove(test_file)
        print("✅ صلاحية الكتابة في المجلد الحالي")
        
        # فحص إنشاء مجلدات
        test_dir = "test_dir"
        os.makedirs(test_dir, exist_ok=True)
        os.rmdir(test_dir)
        print("✅ صلاحية إنشاء مجلدات")
        
        return True
        
    except Exception as e:
        print(f"❌ مشكلة في صلاحيات الملفات: {e}")
        return False

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    required_modules = [
        ('flask', 'Flask'),
        ('werkzeug', 'Werkzeug'),
        ('jinja2', 'Jinja2'),
        ('sqlalchemy', 'SQLAlchemy'),
        ('wtforms', 'WTForms'),
        ('sqlite3', 'SQLite3'),
        ('threading', 'Threading'),
        ('webbrowser', 'WebBrowser'),
        ('socket', 'Socket'),
        ('json', 'JSON'),
        ('datetime', 'DateTime'),
        ('pathlib', 'PathLib')
    ]
    
    missing_modules = []
    
    for module_name, display_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name} غير متاح")
            missing_modules.append(display_name)
    
    if missing_modules:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
        return False
    else:
        print("\n✅ جميع المكتبات المطلوبة متاحة")
        return True

def test_basic_flask_app():
    """اختبار تطبيق Flask أساسي"""
    print("\n🧪 اختبار تطبيق Flask أساسي...")
    
    try:
        from flask import Flask
        
        # إنشاء تطبيق اختبار
        app = Flask(__name__)
        
        @app.route('/')
        def test_route():
            return "اختبار GESTION DES FORMATIONS - النظام يعمل بشكل صحيح!"
        
        # اختبار بدء الخادم
        def run_test_server():
            try:
                app.run(host='127.0.0.1', port=5555, debug=False, use_reloader=False)
            except Exception as e:
                print(f"خطأ في الخادم: {e}")
        
        # بدء الخادم في thread منفصل
        server_thread = threading.Thread(target=run_test_server)
        server_thread.daemon = True
        server_thread.start()
        
        # انتظار قصير لبدء الخادم
        time.sleep(2)
        
        # اختبار الاتصال
        try:
            import urllib.request
            response = urllib.request.urlopen('http://127.0.0.1:5555', timeout=5)
            content = response.read().decode('utf-8')
            
            if 'اختبار GESTION DES FORMATIONS' in content:
                print("✅ اختبار Flask نجح")
                return True
            else:
                print("⚠️ اختبار Flask جزئي")
                return False
                
        except Exception as e:
            print(f"⚠️ لا يمكن اختبار الاتصال: {e}")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار Flask: {e}")
        return False

def generate_compatibility_report():
    """إنشاء تقرير التوافق"""
    print("\n📋 إنشاء تقرير التوافق...")
    
    win_info = get_windows_version()
    
    report = f"""
# تقرير التوافق - GESTION DES FORMATIONS

## معلومات النظام
- **النظام**: {win_info['name']}
- **البناء**: {win_info['build']}
- **الإصدار**: {win_info['version']}
- **المعمارية**: {win_info['architecture']}
- **Python**: {sys.version}

## نتائج الاختبار
- **توافق Windows**: {'✅ متوافق' if check_windows_compatibility() else '❌ غير متوافق'}
- **توافق Python**: {'✅ متوافق' if check_python_compatibility() else '❌ غير متوافق'}
- **قدرات الشبكة**: {'✅ متاحة' if check_network_capabilities() else '❌ محدودة'}
- **صلاحيات الملفات**: {'✅ متاحة' if check_file_permissions() else '❌ محدودة'}
- **المكتبات المطلوبة**: {'✅ متاحة' if check_required_modules() else '❌ مفقودة'}

## التوصيات
- تأكد من تشغيل البرنامج بصلاحيات المدير عند الحاجة
- تأكد من فتح المنفذ 5000 في جدار الحماية للشبكة المحلية
- قم بتحديث Windows إلى أحدث إصدار للحصول على أفضل أداء

تاريخ الاختبار: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    try:
        with open('compatibility_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print("✅ تم حفظ تقرير التوافق: compatibility_report.txt")
    except Exception as e:
        print(f"⚠️ لا يمكن حفظ التقرير: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    print("🎯 هذا الاختبار سيفحص:")
    print("   • توافق إصدار Windows")
    print("   • توافق Python والمكتبات")
    print("   • قدرات الشبكة المحلية")
    print("   • صلاحيات الملفات")
    print("   • اختبار Flask أساسي")
    print()
    
    # تشغيل جميع الاختبارات
    tests = [
        ("توافق Windows", check_windows_compatibility),
        ("توافق Python", check_python_compatibility),
        ("قدرات الشبكة", check_network_capabilities),
        ("صلاحيات الملفات", check_file_permissions),
        ("المكتبات المطلوبة", check_required_modules),
        ("اختبار Flask", test_basic_flask_app)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "🏁" + "=" * 58)
    print("   نتائج اختبار التوافق")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 النتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 النظام متوافق تماماً مع GESTION DES FORMATIONS!")
    elif passed >= total * 0.8:
        print("✅ النظام متوافق مع تحفظات بسيطة")
    else:
        print("⚠️ قد تواجه مشاكل في تشغيل النظام")
    
    # إنشاء تقرير
    generate_compatibility_report()
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
