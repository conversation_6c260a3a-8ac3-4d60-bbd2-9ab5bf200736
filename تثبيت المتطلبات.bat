@echo off
chcp 65001 >nul
title GESTION DES FORMATIONS - تثبيت المتطلبات

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 GESTION DES FORMATIONS                       ║
echo ║                   تثبيت المتطلبات                           ║
echo ║                                                              ║
echo ║                 تطوير: ABOULFADEL.A                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
python --version

echo.
echo 📦 تثبيت المتطلبات الأساسية...

echo   تثبيت Flask...
pip install Flask

echo   تثبيت Flask-SQLAlchemy...
pip install Flask-SQLAlchemy

echo   تثبيت Flask-Login...
pip install Flask-Login

echo   تثبيت Flask-WTF...
pip install Flask-WTF

echo   تثبيت Werkzeug...
pip install Werkzeug

echo   تثبيت Jinja2...
pip install Jinja2

echo   تثبيت WTForms...
pip install WTForms

echo.
echo 📦 تثبيت متطلبات إضافية (اختيارية)...

echo   تثبيت cx_Freeze (لإنشاء ملف تنفيذي)...
pip install cx_Freeze

echo   تثبيت PyInstaller (بديل لإنشاء ملف تنفيذي)...
pip install PyInstaller

echo.
echo ✅ تم تثبيت جميع المتطلبات!

echo.
echo 📋 الخطوات التالية:
echo 1. شغل "تشغيل البرنامج.bat" لبدء التطبيق
echo 2. أو شغل "BUILD_NOW.bat" لإنشاء ملف تنفيذي
echo 3. أو شغل "python start_app.py" مباشرة

echo.
pause
