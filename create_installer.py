"""
سكريبت بناء مثبت Windows باستخدام Inno Setup
لبرنامج GESTION DES FORMATIONS
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_status(message):
    print(f"🔧 {message}")

def check_inno_setup():
    """فحص وجود Inno Setup"""
    print_status("فحص Inno Setup...")
    
    # مسارات Inno Setup المحتملة
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    for path in inno_paths:
        if os.path.exists(path):
            print(f"   ✅ تم العثور على Inno Setup: {path}")
            return path
    
    print("   ❌ Inno Setup غير مثبت")
    print("   💡 يرجى تحميل وتثبيت Inno Setup من:")
    print("   🔗 https://jrsoftware.org/isinfo.php")
    return None

def check_files():
    """فحص الملفات المطلوبة"""
    print_status("فحص الملفات المطلوبة...")
    
    required_files = [
        "installer_simple.iss",
        "GESTION_DES_FORMATIONS_Distribution/GESTION_DES_FORMATIONS.exe",
        "app/static/images/app_icon.ico",
        "app/static/images/Formation-continue-1024x1024.png"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} غير موجود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n   ⚠️ ملفات مفقودة: {len(missing_files)}")
        for file in missing_files:
            print(f"      • {file}")
        return False
    
    return True

def prepare_files():
    """تحضير الملفات للمثبت"""
    print_status("تحضير الملفات...")
    
    # التأكد من وجود مجلد Output
    if not os.path.exists("Output"):
        os.makedirs("Output")
        print("   📁 تم إنشاء مجلد Output")
    
    print("   ✅ تم تحضير الملفات")

def build_installer(inno_path):
    """بناء المثبت"""
    print_status("بناء المثبت...")
    
    script_path = os.path.abspath("installer_simple.iss")
    
    try:
        # تشغيل Inno Setup
        cmd = [inno_path, script_path]
        print(f"   🚀 تشغيل: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("   ✅ تم بناء المثبت بنجاح!")
            return True
        else:
            print("   ❌ فشل في بناء المثبت")
            print("   📋 رسائل الخطأ:")
            if result.stderr:
                print(result.stderr)
            if result.stdout:
                print(result.stdout)
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في عملية البناء: {e}")
        return False

def check_result():
    """فحص نتيجة البناء"""
    print_status("فحص النتيجة...")
    
    setup_file = "Output/GESTION_DES_FORMATIONS_Setup_v1.0.0.exe"
    
    if os.path.exists(setup_file):
        size = os.path.getsize(setup_file)
        size_mb = size / (1024 * 1024)
        
        print(f"   🎉 المثبت تم إنشاؤه بنجاح!")
        print(f"   📍 المسار: {os.path.abspath(setup_file)}")
        print(f"   📊 الحجم: {size_mb:.1f} MB")
        
        return True
    else:
        print("   ❌ ملف المثبت غير موجود")
        
        # فحص مجلد Output
        if os.path.exists("Output"):
            output_files = os.listdir("Output")
            if output_files:
                print(f"   📁 محتويات مجلد Output: {output_files}")
            else:
                print("   📁 مجلد Output فارغ")
        else:
            print("   📁 مجلد Output غير موجود")
        
        return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ GESTION DES FORMATIONS - بناء مثبت Windows")
    print("=" * 60)
    
    # فحص Inno Setup
    inno_path = check_inno_setup()
    if not inno_path:
        input("\nاضغط Enter للخروج...")
        return False
    
    # فحص الملفات
    if not check_files():
        print("\n❌ ملفات مطلوبة مفقودة")
        print("💡 تأكد من تشغيل build_now.py أولاً لإنشاء الملف التنفيذي")
        input("\nاضغط Enter للخروج...")
        return False
    
    # تحضير الملفات
    prepare_files()
    
    # بناء المثبت
    if not build_installer(inno_path):
        print("\n❌ فشل في بناء المثبت")
        input("\nاضغط Enter للخروج...")
        return False
    
    # فحص النتيجة
    if not check_result():
        print("\n❌ فشل في إنشاء المثبت")
        input("\nاضغط Enter للخروج...")
        return False
    
    # النتيجة النهائية
    print("\n" + "🎉" + "=" * 58)
    print("   تم إنشاء مثبت Windows بنجاح!")
    print("=" * 60)
    
    print("\n📦 ملف المثبت: Output/GESTION_DES_FORMATIONS_Setup_v1.0.0.exe")
    
    print("\n🚀 كيفية الاستخدام:")
    print("   1. انسخ ملف المثبت إلى الجهاز المطلوب")
    print("   2. شغل المثبت بصلاحيات المدير")
    print("   3. اتبع خطوات التثبيت")
    print("   4. شغل البرنامج من قائمة البرامج")
    
    print("\n✅ المميزات:")
    print("   • يدعم جميع إصدارات Windows (7-11)")
    print("   • يدعم أنظمة 32 و 64 بت")
    print("   • إعداد تلقائي لجدار الحماية")
    print("   • دعم الشبكة المحلية")
    print("   • قاعدة بيانات مركزية")
    print("   • إزالة نظيفة")
    
    input("\nاضغط Enter للخروج...")
    return True

if __name__ == "__main__":
    main()
