"""
سكريبت تحويل الشعار إلى أيقونة ICO
لاستخدامها في الملف التنفيذي
"""

import os
import sys
from pathlib import Path

def install_pillow():
    """تثبيت مكتبة Pillow إذا لم تكن مثبتة"""
    try:
        from PIL import Image
        print("✅ مكتبة Pillow متاحة")
        return True
    except ImportError:
        print("📦 تثبيت مكتبة Pillow...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
            print("✅ تم تثبيت Pillow بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت Pillow: {e}")
            return False

def create_icon_from_logo():
    """تحويل الشعار إلى أيقونة ICO"""
    
    # مسارات الملفات
    logo_path = "app/static/images/Formation-continue-1024x1024.png"
    icon_path = "app/static/images/app_icon.ico"
    
    print("🎨 تحويل الشعار إلى أيقونة...")
    print(f"📍 الشعار: {logo_path}")
    print(f"📍 الأيقونة: {icon_path}")
    
    # التحقق من وجود الشعار
    if not os.path.exists(logo_path):
        print(f"❌ الشعار غير موجود: {logo_path}")
        return False
    
    # تثبيت Pillow إذا لم تكن مثبتة
    if not install_pillow():
        return False
    
    try:
        from PIL import Image
        
        # فتح الصورة
        print("📖 قراءة الشعار...")
        img = Image.open(logo_path)
        
        # تحويل إلى RGBA إذا لم تكن كذلك
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
            print("🔄 تم تحويل الصورة إلى RGBA")
        
        # إنشاء أحجام مختلفة للأيقونة
        sizes = [
            (16, 16),    # أيقونة صغيرة
            (32, 32),    # أيقونة متوسطة
            (48, 48),    # أيقونة كبيرة
            (64, 64),    # أيقونة كبيرة جداً
            (128, 128),  # أيقونة عالية الدقة
            (256, 256)   # أيقونة عالية الدقة جداً
        ]
        
        print(f"🔧 إنشاء أيقونة بأحجام: {sizes}")
        
        # حفظ كأيقونة ICO
        img.save(icon_path, format='ICO', sizes=sizes)
        
        # التحقق من إنشاء الأيقونة
        if os.path.exists(icon_path):
            size = os.path.getsize(icon_path)
            size_kb = size / 1024
            print(f"✅ تم إنشاء الأيقونة بنجاح!")
            print(f"📊 الحجم: {size_kb:.1f} KB")
            return True
        else:
            print("❌ فشل في إنشاء الأيقونة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحويل الأيقونة: {e}")
        return False

def create_favicon():
    """إنشاء favicon.ico للمتصفح"""
    
    logo_path = "app/static/images/Formation-continue-1024x1024.png"
    favicon_path = "app/static/images/favicon.ico"
    
    if not os.path.exists(logo_path):
        print(f"⚠️ لا يمكن إنشاء favicon: الشعار غير موجود")
        return False
    
    try:
        from PIL import Image
        
        print("🌐 إنشاء favicon للمتصفح...")
        
        # فتح الصورة
        img = Image.open(logo_path)
        
        # تحويل إلى RGBA
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # أحجام favicon
        favicon_sizes = [(16, 16), (32, 32), (48, 48)]
        
        # حفظ كـ favicon
        img.save(favicon_path, format='ICO', sizes=favicon_sizes)
        
        if os.path.exists(favicon_path):
            print(f"✅ تم إنشاء favicon: {favicon_path}")
            return True
        else:
            print("❌ فشل في إنشاء favicon")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء favicon: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎨 إنشاء الأيقونات من الشعار")
    print("=" * 50)
    
    success = True
    
    # إنشاء أيقونة التطبيق
    if create_icon_from_logo():
        print("✅ تم إنشاء أيقونة التطبيق")
    else:
        print("❌ فشل في إنشاء أيقونة التطبيق")
        success = False
    
    print()
    
    # إنشاء favicon
    if create_favicon():
        print("✅ تم إنشاء favicon")
    else:
        print("❌ فشل في إنشاء favicon")
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 تم إنشاء جميع الأيقونات بنجاح!")
        print("\n📁 الملفات المُنشأة:")
        
        files = [
            "app/static/images/app_icon.ico",
            "app/static/images/favicon.ico"
        ]
        
        for file in files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                size_kb = size / 1024
                print(f"   ✅ {file} ({size_kb:.1f} KB)")
            else:
                print(f"   ❌ {file} (غير موجود)")
        
        print("\n💡 يمكن الآن استخدام هذه الأيقونات في:")
        print("   • الملف التنفيذي (app_icon.ico)")
        print("   • المتصفح (favicon.ico)")
        
    else:
        print("⚠️ تم إنشاء بعض الأيقونات فقط")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
