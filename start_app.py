"""
تشغيل التطبيق مباشرة بدون الحاجة لملف تنفيذي
يعمل على أي جهاز عليه Python
"""

import os
import sys
import webbrowser
import threading
import time
import socket
import subprocess

def check_python_packages():
    """التحقق من وجود الحزم المطلوبة وتثبيتها إذا لزم الأمر"""
    
    required_packages = [
        'flask', 'flask-sqlalchemy', 'flask-login', 'flask-wtf', 
        'werkzeug', 'jinja2', 'wtforms'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("📦 تثبيت الحزم المفقودة...")
        for package in missing_packages:
            print(f"   تثبيت {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"   ✅ {package} تم تثبيته")
            except subprocess.CalledProcessError:
                print(f"   ❌ فشل في تثبيت {package}")
                return False
    
    return True

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)  # انتظار 3 ثوان لبدء الخادم
    webbrowser.open("http://127.0.0.1:5000")

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "GESTION DES FORMATIONS.lnk")
        target = sys.executable
        wDir = os.getcwd()
        arguments = f'"{os.path.join(wDir, "start_app.py")}"'
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.Arguments = arguments
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = target
        shortcut.save()
        
        print(f"✅ تم إنشاء اختصار على سطح المكتب: {path}")
        
    except ImportError:
        print("💡 لإنشاء اختصار على سطح المكتب، ثبت: pip install pywin32 winshell")
    except Exception as e:
        print(f"⚠️ لم يتم إنشاء اختصار سطح المكتب: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 GESTION DES FORMATIONS - تشغيل مباشر")
    print("   تطوير: ABOULFADEL.A")
    print("=" * 60)
    
    # التحقق من وجود الملفات الأساسية
    if not os.path.exists('run.py'):
        print("❌ خطأ: ملف run.py غير موجود")
        print("💡 تأكد من أنك في مجلد المشروع الصحيح")
        input("اضغط Enter للخروج...")
        return
    
    if not os.path.exists('app'):
        print("❌ خطأ: مجلد app غير موجود")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ الملفات الأساسية موجودة")
    
    # التحقق من الحزم وتثبيتها
    if not check_python_packages():
        print("❌ فشل في تثبيت الحزم المطلوبة")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع الحزم متوفرة")
    
    # الحصول على عنوان IP
    local_ip = get_local_ip()
    
    print(f"\n🌐 معلومات الاتصال:")
    print(f"   محلي:  http://127.0.0.1:5000")
    print(f"   شبكة:  http://{local_ip}:5000")
    
    print(f"\n👥 للاستخدام متعدد المستخدمين:")
    print(f"   الأجهزة الأخرى في الشبكة يمكنها الوصول عبر:")
    print(f"   http://{local_ip}:5000")
    
    print(f"\n🔐 بيانات الدخول الافتراضية:")
    print(f"   المستخدم: admin")
    print(f"   كلمة المرور: admin123")
    
    # إنشاء اختصار سطح المكتب
    create_desktop_shortcut()
    
    print(f"\n🚀 بدء تشغيل الخادم...")
    print(f"⚠️  لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 60)
    
    # فتح المتصفح في thread منفصل
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # تشغيل التطبيق
        from app import create_app
        app = create_app()
        
        app.run(
            host='0.0.0.0',  # للسماح بالاتصالات الخارجية
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("\n💡 الحلول المقترحة:")
        print("1. تأكد من أن المنفذ 5000 غير مستخدم")
        print("2. أعد تشغيل الكمبيوتر")
        print("3. شغل البرنامج كمدير")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
