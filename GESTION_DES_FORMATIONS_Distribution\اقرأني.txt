# GESTION DES FORMATIONS
## نظام إدارة التكوين والتدريب

### معلومات النظام
- **الاسم**: GESTION DES FORMATIONS
- **الإصدار**: 1.0.0 (محسن)
- **المطور**: ABOULFADEL.A
- **التاريخ**: 2024

### متطلبات التشغيل
✅ Windows 7, 8, 8.1, 10, 11
✅ أنظمة 32 و 64 بت
✅ لا يحتاج Python أو برامج إضافية
✅ يدعم الشبكة المحلية (LAN)
✅ يدعم عدة مستخدمين متزامنين

### كيفية التشغيل
1. شغل ملف `GESTION_DES_FORMATIONS.exe`
2. أو شغل `تشغيل_النظام.bat` لمعلومات إضافية
3. سيفتح النظام في المتصفح تلقائياً

### للوصول من الشبكة المحلية
- شغل البرنامج على الخادم الرئيسي
- سيعرض البرنامج عنوان IP للوصول من أجهزة أخرى
- مثال: http://*************:5000

### قاعدة البيانات
- قاعدة البيانات مركزية على الجهاز الذي يشغل البرنامج
- جميع المستخدمين يصلون لنفس البيانات
- يتم إنشاء مجلد `data` تلقائياً لحفظ البيانات
- النسخ الاحتياطية تُحفظ في مجلد `backups`

### المستخدم الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- ⚠️ يرجى تغيير كلمة المرور بعد أول تسجيل دخول

### إعدادات جدار الحماية
إذا لم تتمكن من الوصول من أجهزة أخرى:
1. افتح موجه الأوامر كمدير
2. شغل الأوامر التالية:
```
netsh advfirewall firewall add rule name="GESTION DES FORMATIONS" dir=in action=allow program="المسار\GESTION_DES_FORMATIONS.exe"
netsh advfirewall firewall add rule name="GESTION DES FORMATIONS Port" dir=in action=allow protocol=TCP localport=5000
```

### استكشاف الأخطاء
- **البرنامج لا يبدأ**: شغله بصلاحيات المدير
- **لا يفتح المتصفح**: افتح المتصفح يدوياً واذهب إلى http://localhost:5000
- **مشاكل الشبكة**: تحقق من إعدادات جدار الحماية
- **أخطاء أخرى**: راجع ملف `app.log` في نفس مجلد البرنامج

### الملفات المُنشأة تلقائياً
- `data/` - قاعدة البيانات والبيانات
- `backups/` - النسخ الاحتياطية
- `logs/` - ملفات السجل
- `app.log` - سجل التطبيق الرئيسي

### الدعم الفني
للمساعدة أو الاستفسارات، يرجى التواصل مع المطور.

---
© 2024 ABOULFADEL.A - جميع الحقوق محفوظة
