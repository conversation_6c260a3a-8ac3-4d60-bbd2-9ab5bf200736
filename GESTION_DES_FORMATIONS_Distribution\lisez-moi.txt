# GESTION DES FORMATIONS
## Système de gestion des formations et de la formation

### Informations système
- **Nom**: GESTION DES FORMATIONS
- **Version**: 1.0.0 (optimisé)
- **Développeur**: ABOULFADEL.A
- **Date**: 2024

### Configuration requise
✅ Windows 7, 8, 8.1, 10, 11
✅ Systèmes 32 et 64 bits
✅ Aucun Python ou logiciel supplémentaire requis
✅ Support réseau local (LAN)
✅ Support multi-utilisateurs simultanés

### Comment utiliser
1. Exécutez le fichier `GESTION_DES_FORMATIONS.exe`
2. Ou exécutez `تشغيل_النظام.bat` pour des informations supplémentaires
3. Le système s'ouvrira automatiquement dans le navigateur

### Pour l'accès réseau local
- Exécutez le programme sur le serveur principal
- Le programme affichera l'adresse IP pour l'accès depuis d'autres appareils
- Exemple: http://*************:5000

### Base de données
- Base de données centralisée sur l'ordinateur qui exécute le programme
- Tous les utilisateurs accèdent aux mêmes données
- Le dossier `data` sera créé automatiquement pour sauvegarder les données
- Les sauvegardes sont conservées dans le dossier `backups`

### Utilisateur par défaut
- **Nom d'utilisateur**: admin
- **Mot de passe**: admin123
- ⚠️ Veuillez changer le mot de passe après la première connexion

### Paramètres du pare-feu
Si vous ne pouvez pas accéder depuis d'autres appareils:
1. Ouvrez l'invite de commande en tant qu'administrateur
2. Exécutez les commandes suivantes:
```
netsh advfirewall firewall add rule name="GESTION DES FORMATIONS" dir=in action=allow program="Chemin\GESTION_DES_FORMATIONS.exe"
netsh advfirewall firewall add rule name="GESTION DES FORMATIONS Port" dir=in action=allow protocol=TCP localport=5000
```

### Ou depuis l'interface Windows:
1. Allez dans Paramètres Windows > Mise à jour et sécurité > Sécurité Windows
2. Choisissez "Pare-feu et protection réseau"
3. Choisissez "Autoriser une application via le pare-feu"
4. Ajoutez GESTION_DES_FORMATIONS.exe

### Fichiers créés automatiquement
- `data/` - Base de données et données
- `backups/` - Sauvegardes automatiques
- `logs/` - Fichiers de journal et erreurs
- `app.log` - Journal principal de l'application

### Dépannage

#### Problèmes courants:
1. **Le programme ne démarre pas**:
   - Exécutez-le avec les privilèges administrateur
   - Vérifiez l'espace disque disponible
   - Consultez le fichier logs/app.log

2. **Impossible d'accéder depuis le réseau**:
   - Vérifiez les paramètres du pare-feu
   - Assurez-vous que les appareils sont sur le même réseau
   - Essayez une adresse IP différente

3. **Problèmes de base de données**:
   - Vérifiez les autorisations du dossier data
   - Assurez-vous que la base de données n'est pas utilisée par un autre programme

### Fichiers de journal:
- `logs/app.log` - Journal principal de l'application
- `data/config.ini` - Paramètres système

### Support technique
- **Développeur**: ABOULFADEL.A
- **Version**: 1.0.0
- **Date de sortie**: 2024

### Pour obtenir de l'aide:
- Consultez ce fichier "lisez-moi.txt" dans le dossier d'installation
- Vérifiez les fichiers de journal dans le dossier logs
- Assurez-vous que Windows est à jour

---
© 2024 ABOULFADEL.A - Tous droits réservés

**Note**: Ce programme a été créé avec PyInstaller et est entièrement sûr à utiliser sur tous les systèmes Windows pris en charge.
