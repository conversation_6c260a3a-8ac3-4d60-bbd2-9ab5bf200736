# 🚀 دليل الاستخدام - GESTION DES FORMATIONS

## 📋 **3 طرق لتشغيل البرنامج**

---

## 🎯 **الطريقة 1: تشغيل مباشر (الأسهل)**

### خطوة واحدة فقط:
```
📁 اضغط مرتين على: "تشغيل البرنامج.bat"
```

### ما يحدث:
- ✅ يتحقق من وجود Python
- ✅ يثبت المتطلبات تلقائياً إذا لزم الأمر
- ✅ يشغل البرنامج
- ✅ يفتح المتصفح تلقائياً

---

## 🔧 **الطريقة 2: إنشاء ملف تنفيذي**

### إذا كنت تريد ملف .exe:

#### أ) تثبيت المتطلبات أولاً:
```
📁 اضغط مرتين على: "تثبيت المتطلبات.bat"
```

#### ب) إنشاء الملف التنفيذي:
```
📁 اضغط مرتين على: "BUILD_NOW.bat"
```

#### ج) النتيجة:
```
📁 build/
  └── 📁 exe.xxx/
      ├── 🔧 GestionFormations.exe  ← ملفك التنفيذي
      └── ملفات أخرى...
```

---

## ⚡ **الطريقة 3: تشغيل يدوي**

### في Command Prompt:
```bash
# 1. تثبيت المتطلبات
pip install flask flask-sqlalchemy flask-login flask-wtf

# 2. تشغيل البرنامج
python start_app.py
```

---

## 🌐 **الاستخدام متعدد المستخدمين**

### على الجهاز الخادم (حيث البرنامج):
1. شغل البرنامج بأي طريقة من الطرق أعلاه
2. ستظهر رسالة مثل:
   ```
   🌐 معلومات الاتصال:
      محلي:  http://127.0.0.1:5000
      شبكة:  http://*************:5000
   ```

### على الأجهزة الأخرى في الشبكة:
1. افتح أي متصفح
2. اكتب العنوان: `http://*************:5000`
3. ادخل بالبيانات: `admin` / `admin123`

---

## 🔐 **بيانات الدخول الافتراضية**

```
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
```

---

## 📁 **ملفات النظام المهمة**

### ملفات التشغيل:
- `تشغيل البرنامج.bat` - تشغيل مباشر
- `start_app.py` - تشغيل Python مباشر
- `run.py` - ملف التشغيل الأصلي

### ملفات إنشاء الملف التنفيذي:
- `BUILD_NOW.bat` - إنشاء تلقائي
- `build_simple_exe.py` - بناء مع cx_Freeze
- `build_with_pyinstaller.py` - بناء مع PyInstaller

### ملفات التشخيص:
- `check_system.py` - فحص النظام
- `diagnostic_backups.py` - فحص النسخ الاحتياطية

---

## 🛠️ **حل المشاكل الشائعة**

### مشكلة: "Python غير موجود"
**الحل:**
1. ثبت Python من: https://www.python.org/downloads/
2. تأكد من تحديد "Add Python to PATH"

### مشكلة: "المنفذ مستخدم"
**الحل:**
1. أغلق جميع نوافذ المتصفح
2. أعد تشغيل الكمبيوتر
3. شغل البرنامج مرة أخرى

### مشكلة: "لا يمكن الوصول من أجهزة أخرى"
**الحل:**
1. تأكد من أن Firewall يسمح بالمنفذ 5000
2. تأكد من أن الأجهزة في نفس الشبكة
3. جرب إيقاف Firewall مؤقتاً للاختبار

### مشكلة: "فشل في إنشاء الملف التنفيذي"
**الحل:**
1. شغل `python check_system.py` للتشخيص
2. جرب PyInstaller بدلاً من cx_Freeze
3. تأكد من إغلاق البرنامج قبل البناء

---

## 📊 **مميزات النظام**

### ✅ **إدارة التكوينات:**
- إضافة وتعديل التكوينات
- جدولة التكوينات
- تتبع المشاركين

### ✅ **إدارة المستخدمين:**
- نظام تسجيل دخول آمن
- صلاحيات مختلفة
- تتبع النشاطات

### ✅ **النسخ الاحتياطية:**
- نسخ احتياطية تلقائية
- استيراد وتصدير البيانات
- حماية من فقدان البيانات

### ✅ **التقارير:**
- تقارير شاملة
- إحصائيات مفصلة
- تصدير للملفات

---

## 📞 **الدعم الفني**

**المطور:** ABOULFADEL.A  
**الإصدار:** 1.0.0  
**التاريخ:** 2024

للمساعدة التقنية، اتصل بالمطور.

---

## 🎉 **ملاحظة مهمة**

**الطريقة الأولى (تشغيل البرنامج.bat) هي الأسهل والأسرع!**

لا تحتاج لإنشاء ملف تنفيذي إلا إذا كنت تريد توزيع البرنامج على أجهزة لا تحتوي على Python.

**للاستخدام اليومي، استخدم "تشغيل البرنامج.bat" مباشرة!** 🚀
