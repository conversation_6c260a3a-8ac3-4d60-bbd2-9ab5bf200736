"""
سكريبت بناء محسن لإنشاء ملف تنفيذي يدعم جميع إصدارات Windows
يدعم الشبكة المحلية وقاعدة البيانات المركزية
"""

import sys
import os
import subprocess
import shutil
from pathlib import Path

print("🔧 بناء الملف التنفيذي - GESTION DES FORMATIONS")
print("=" * 60)

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")

    # التحقق من الملفات الأساسية
    required_files = ['run.py', 'config.py', 'requirements.txt']
    required_dirs = ['app', 'app/static', 'app/templates']

    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ خطأ: ملف {file} غير موجود")
            return False
        print(f"✅ {file}")

    for dir in required_dirs:
        if not os.path.exists(dir):
            print(f"❌ خطأ: مجلد {dir} غير موجود")
            return False
        print(f"✅ {dir}")

    # التحقق من الشعار
    logo_path = "app/static/images/Formation-continue-1024x1024.png"
    if os.path.exists(logo_path):
        print(f"✅ الشعار موجود: {logo_path}")
    else:
        print(f"⚠️ تحذير: الشعار غير موجود: {logo_path}")

    return True

def install_pyinstaller():
    """تثبيت PyInstaller إذا لم يكن مثبتاً"""
    try:
        import PyInstaller
        print("✅ PyInstaller متاح")
        return True
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=6.0.0"])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت PyInstaller: {e}")
            return False

def create_icon():
    """تحويل الشعار إلى أيقونة ICO"""
    logo_path = "app/static/images/Formation-continue-1024x1024.png"
    icon_path = "app/static/images/app_icon.ico"

    if not os.path.exists(logo_path):
        print("⚠️ الشعار غير موجود، سيتم استخدام الأيقونة الافتراضية")
        return None

    try:
        from PIL import Image
        print("🎨 تحويل الشعار إلى أيقونة...")

        # فتح الصورة وتحويلها إلى ICO
        img = Image.open(logo_path)
        img = img.convert('RGBA')

        # إنشاء أحجام مختلفة للأيقونة
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        img.save(icon_path, format='ICO', sizes=sizes)

        print(f"✅ تم إنشاء الأيقونة: {icon_path}")
        return icon_path

    except ImportError:
        print("📦 تثبيت Pillow لتحويل الأيقونة...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
            from PIL import Image

            img = Image.open(logo_path)
            img = img.convert('RGBA')
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
            img.save(icon_path, format='ICO', sizes=sizes)

            print(f"✅ تم إنشاء الأيقونة: {icon_path}")
            return icon_path

        except Exception as e:
            print(f"⚠️ فشل في تحويل الأيقونة: {e}")
            return None
    except Exception as e:
        print(f"⚠️ فشل في تحويل الأيقونة: {e}")
        return None

def create_spec_file():
    """إنشاء ملف spec محسن لـ PyInstaller"""
    icon_path = create_icon()

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين PyInstaller لبرنامج GESTION DES FORMATIONS
يدعم جميع إصدارات Windows (7, 8, 8.1, 10, 11) - 32/64 بت
"""

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# مسار المشروع
project_path = os.path.abspath('.')

# جمع ملفات Flask والقوالب
flask_datas = collect_data_files('flask')
jinja2_datas = collect_data_files('jinja2')

# ملفات التطبيق
app_datas = [
    ('app', 'app'),
    ('config.py', '.'),
]

# إضافة قاعدة البيانات إذا كانت موجودة
if os.path.exists('app.db'):
    app_datas.append(('app.db', '.'))

# جمع جميع الملفات
all_datas = flask_datas + jinja2_datas + app_datas

# المكتبات المخفية المطلوبة
hiddenimports = [
    'flask',
    'flask.app',
    'flask.blueprints',
    'flask.globals',
    'flask.helpers',
    'flask.json',
    'flask.logging',
    'flask.sessions',
    'flask.signals',
    'flask.templating',
    'flask.testing',
    'flask.views',
    'flask.wrappers',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'werkzeug.exceptions',
    'werkzeug.routing',
    'werkzeug.security',
    'jinja2',
    'jinja2.ext',
    'jinja2.loaders',
    'jinja2.runtime',
    'jinja2.utils',
    'sqlalchemy',
    'sqlalchemy.dialects.sqlite',
    'sqlalchemy.engine',
    'sqlalchemy.orm',
    'sqlalchemy.pool',
    'flask_sqlalchemy',
    'flask_login',
    'flask_wtf',
    'wtforms',
    'wtforms.fields',
    'wtforms.validators',
    'wtforms.widgets',
    'email_validator',
    'sqlite3',
    'threading',
    'webbrowser',
    'socket',
    'time',
    'datetime',
    'json',
    'uuid',
    'hashlib',
    'secrets',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['run.py'],
    pathex=[project_path],
    binaries=[],
    datas=all_datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL.ImageTk',
        'PIL.ImageWin',
        'test',
        'unittest',
        'doctest',
        'pdb',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات المكررة
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GESTION_DES_FORMATIONS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{icon_path}' if icon_path else None,
    version_info={{
        'version': '*******',
        'description': 'GESTION DES FORMATIONS - نظام إدارة التكوين',
        'product_name': 'GESTION DES FORMATIONS',
        'product_version': '1.0.0',
        'company_name': 'ABOULFADEL.A',
        'file_description': 'نظام إدارة التكوين والتدريب',
        'internal_name': 'GestionFormations',
        'copyright': '© 2024 ABOULFADEL.A',
        'original_filename': 'GESTION_DES_FORMATIONS.exe',
    }}
)
'''

    with open('gestion_formations.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ تم إنشاء ملف gestion_formations.spec")
    return 'gestion_formations.spec'

def build_exe(spec_file):
    """بناء الملف التنفيذي باستخدام PyInstaller"""
    print(f"\n🔨 بناء الملف التنفيذي من {spec_file}...")

    try:
        # تنظيف المجلدات السابقة
        if os.path.exists('dist'):
            shutil.rmtree('dist')
            print("🧹 تم حذف مجلد dist السابق")

        if os.path.exists('build'):
            shutil.rmtree('build')
            print("🧹 تم حذف مجلد build السابق")

        # تشغيل PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
        print(f"🚀 تشغيل: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي")
            print("📋 رسائل الخطأ:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ خطأ في عملية البناء: {e}")
        return False

def check_result():
    """فحص نتيجة البناء"""
    print("\n🔍 فحص النتيجة...")

    exe_path = "dist/GESTION_DES_FORMATIONS.exe"

    if os.path.exists(exe_path):
        # حساب الحجم
        size = os.path.getsize(exe_path)
        size_mb = size / (1024 * 1024)

        print(f"🎉 الملف التنفيذي تم إنشاؤه بنجاح!")
        print(f"📍 المسار: {os.path.abspath(exe_path)}")
        print(f"📊 الحجم: {size_mb:.1f} MB")

        # فحص الملفات المرافقة
        dist_files = os.listdir('dist')
        print(f"📁 ملفات المجلد dist: {len(dist_files)} ملف")

        return True
    else:
        print("❌ الملف التنفيذي غير موجود")

        # فحص مجلد dist
        if os.path.exists('dist'):
            dist_files = os.listdir('dist')
            print(f"📁 محتويات مجلد dist: {dist_files}")
        else:
            print("📁 مجلد dist غير موجود")

        return False

def create_launcher_script():
    """إنشاء سكريبت تشغيل للشبكة المحلية"""
    launcher_content = '''@echo off
chcp 65001 > nul
title GESTION DES FORMATIONS - نظام إدارة التكوين

echo.
echo ========================================
echo   GESTION DES FORMATIONS
echo   نظام إدارة التكوين والتدريب
echo   ABOULFADEL.A
echo ========================================
echo.

echo 🚀 بدء تشغيل النظام...
echo.

REM الحصول على عنوان IP المحلي
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        goto :found
    )
)
:found

echo 🌐 عنوان الخادم المحلي: %LOCAL_IP%:5000
echo.
echo 📋 للوصول من أجهزة أخرى في الشبكة:
echo    http://%LOCAL_IP%:5000
echo.
echo ⚠️  تأكد من أن جدار الحماية يسمح بالاتصال على المنفذ 5000
echo.

REM تشغيل البرنامج
GESTION_DES_FORMATIONS.exe

pause
'''

    with open('dist/تشغيل_الخادم.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_content)

    print("✅ تم إنشاء سكريبت تشغيل الخادم")

def main():
    """الدالة الرئيسية"""
    print("🎯 هدف البناء: ملف تنفيذي يدعم:")
    print("   • جميع إصدارات Windows (7, 8, 8.1, 10, 11)")
    print("   • أنظمة 32 و 64 بت")
    print("   • الشبكة المحلية (LAN)")
    print("   • قاعدة بيانات مركزية")
    print("   • عمل متعدد المستخدمين")
    print()

    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return False

    # تثبيت PyInstaller
    if not install_pyinstaller():
        print("\n❌ فشل في تثبيت PyInstaller")
        input("اضغط Enter للخروج...")
        return False

    # إنشاء ملف spec
    spec_file = create_spec_file()

    # بناء الملف التنفيذي
    if not build_exe(spec_file):
        print("\n❌ فشل في بناء الملف التنفيذي")
        input("اضغط Enter للخروج...")
        return False

    # فحص النتيجة
    if not check_result():
        print("\n❌ فشل في إنشاء الملف التنفيذي")
        input("اضغط Enter للخروج...")
        return False

    # إنشاء سكريبت التشغيل
    create_launcher_script()

    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الملف التنفيذي بنجاح!")
    print("\n📋 كيفية الاستخدام:")
    print("1. اذهب إلى مجلد dist/")
    print("2. شغل GESTION_DES_FORMATIONS.exe")
    print("3. أو شغل تشغيل_الخادم.bat لعرض معلومات الشبكة")

    print("\n🌐 للوصول من الشبكة المحلية:")
    print("- شغل البرنامج على الخادم الرئيسي")
    print("- استخدم عنوان IP المعروض للوصول من أجهزة أخرى")
    print("- مثال: http://*************:5000")

    print("\n💾 قاعدة البيانات:")
    print("- ستكون مركزية على الجهاز الذي يشغل البرنامج")
    print("- جميع المستخدمين سيصلون لنفس البيانات")

    print("\n📦 للتوزيع:")
    print("- انسخ كامل مجلد dist/ إلى الجهاز المطلوب")
    print("- لا حاجة لتثبيت Python أو أي برامج إضافية")

    print("\n✅ متوافق مع:")
    print("- Windows 7, 8, 8.1, 10, 11")
    print("- أنظمة 32 و 64 بت")
    print("- الشبكة المحلية (LAN)")

    input("\nاضغط Enter للخروج...")
    return True

if __name__ == "__main__":
    main()
