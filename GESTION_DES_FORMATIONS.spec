# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run_exe.py'],
    pathex=[],
    binaries=[],
    datas=[('app', 'app'), ('config_production.py', '.')],
    hiddenimports=['flask', 'flask_sqlalchemy', 'flask_login', 'flask_wtf', 'wtforms', 'sqlalchemy', 'werkzeug', 'jinja2', 'sqlite3', 'threading', 'webbrowser', 'socket', 'time', 'datetime', 'json', 'pathlib', 'secrets'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='GESTION_DES_FORMATIONS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['app\\static\\images\\app_icon.ico'],
)
