"""
Script pour mettre à jour la base de données avec les nouveaux modèles
"""

import os
import sys
from datetime import datetime, timezone

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import DossierAdministratif, DossierRemboursement

def update_database():
    """Mettre à jour la base de données avec les nouveaux champs"""
    app = create_app()
    
    with app.app_context():
        print("🔄 Mise à jour de la base de données...")
        
        try:
            # C<PERSON>er toutes les tables (y compris les nouvelles)
            db.create_all()
            print("✅ Tables créées/mises à jour avec succès")
            
            # Vérifier si la colonne F3 existe dans DossierRemboursement
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('dossier_remboursement')]
            
            if 'f3' not in columns:
                print("🔧 Ajout de la colonne F3 à DossierRemboursement...")
                try:
                    db.engine.execute('ALTER TABLE dossier_remboursement ADD COLUMN f3 BOOLEAN DEFAULT 0')
                    print("✅ Colonne F3 ajoutée avec succès")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'ajout de F3: {e}")
            else:
                print("✅ Colonne F3 déjà présente")
            
            # Vérifier si la table DossierAdministratif existe
            tables = inspector.get_table_names()
            if 'dossier_administratif' not in tables:
                print("🔧 Création de la table DossierAdministratif...")
                DossierAdministratif.__table__.create(db.engine)
                print("✅ Table DossierAdministratif créée avec succès")
            else:
                print("✅ Table DossierAdministratif déjà présente")
            
            db.session.commit()
            print("🎉 Mise à jour de la base de données terminée avec succès!")
            
        except Exception as e:
            print(f"❌ Erreur lors de la mise à jour: {e}")
            db.session.rollback()
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 GESTION DES FORMATIONS - Mise à jour de la base de données")
    print("=" * 60)
    
    if update_database():
        print("\n✅ La base de données a été mise à jour avec succès!")
        print("📋 Nouvelles fonctionnalités ajoutées:")
        print("   • Nouveau modèle: Dossiers Administratif")
        print("   • Nouveau champ F3 dans Dossiers de Remboursement")
    else:
        print("\n❌ Échec de la mise à jour de la base de données")
    
    input("\nAppuyez sur Entrée pour continuer...")
