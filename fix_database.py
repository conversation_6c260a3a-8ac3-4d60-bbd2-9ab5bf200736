"""
Script simple pour ajouter la colonne F3 à la base de données
"""

import sqlite3
import os

def fix_database():
    """Ajouter la colonne F3 à la table dossier_remboursement"""
    
    # Chemins possibles pour la base de données
    db_paths = [
        'app.db',
        'data/gestion_formations.db',
        'GESTION_DES_FORMATIONS_Distribution/data/gestion_formations.db'
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"🔧 Base de données trouvée: {db_path}")
            
            try:
                # Connexion à la base de données
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Vérifier si la colonne f3 existe déjà
                cursor.execute("PRAGMA table_info(dossier_remboursement)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'f3' not in columns:
                    print("➕ Ajout de la colonne F3...")
                    cursor.execute("ALTER TABLE dossier_remboursement ADD COLUMN f3 BOOLEAN DEFAULT 0")
                    conn.commit()
                    print("✅ Colonne F3 ajoutée avec succès!")
                else:
                    print("✅ Colonne F3 déjà présente")
                
                # Vérifier si la table dossier_administratif existe
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dossier_administratif'")
                if not cursor.fetchone():
                    print("➕ Création de la table dossier_administratif...")
                    cursor.execute("""
                        CREATE TABLE dossier_administratif (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            fiche_inscription_id INTEGER,
                            f1 BOOLEAN DEFAULT 0,
                            rc BOOLEAN DEFAULT 0,
                            statut BOOLEAN DEFAULT 0,
                            declaration_sur_honneur BOOLEAN DEFAULT 0,
                            pv BOOLEAN DEFAULT 0,
                            procuration BOOLEAN DEFAULT 0,
                            attestation_rib BOOLEAN DEFAULT 0,
                            observations TEXT,
                            piece_jointe VARCHAR(255),
                            date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
                            date_modification DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (fiche_inscription_id) REFERENCES fiche_inscription (id)
                        )
                    """)
                    conn.commit()
                    print("✅ Table dossier_administratif créée avec succès!")
                else:
                    print("✅ Table dossier_administratif déjà présente")

                    # Vérifier et ajouter les nouvelles colonnes si nécessaires
                    cursor.execute("PRAGMA table_info(dossier_administratif)")
                    admin_columns = [column[1] for column in cursor.fetchall()]

                    if 'observations' not in admin_columns:
                        print("➕ Ajout de la colonne observations...")
                        cursor.execute("ALTER TABLE dossier_administratif ADD COLUMN observations TEXT")
                        conn.commit()
                        print("✅ Colonne observations ajoutée!")

                    if 'piece_jointe' not in admin_columns:
                        print("➕ Ajout de la colonne piece_jointe...")
                        cursor.execute("ALTER TABLE dossier_administratif ADD COLUMN piece_jointe VARCHAR(255)")
                        conn.commit()
                        print("✅ Colonne piece_jointe ajoutée!")

                # Vérifier et ajouter M1, F2, F3 à dossier_technique
                cursor.execute("PRAGMA table_info(dossier_technique)")
                tech_columns = [column[1] for column in cursor.fetchall()]

                if 'm1' not in tech_columns:
                    print("➕ Ajout de la colonne M1 à dossier_technique...")
                    cursor.execute("ALTER TABLE dossier_technique ADD COLUMN m1 BOOLEAN DEFAULT 0")
                    conn.commit()
                    print("✅ Colonne M1 ajoutée!")

                if 'f2' not in tech_columns:
                    print("➕ Ajout de la colonne F2 à dossier_technique...")
                    cursor.execute("ALTER TABLE dossier_technique ADD COLUMN f2 BOOLEAN DEFAULT 0")
                    conn.commit()
                    print("✅ Colonne F2 ajoutée!")

                if 'f3' not in tech_columns:
                    print("➕ Ajout de la colonne F3 à dossier_technique...")
                    cursor.execute("ALTER TABLE dossier_technique ADD COLUMN f3 BOOLEAN DEFAULT 0")
                    conn.commit()
                    print("✅ Colonne F3 ajoutée!")
                
                conn.close()
                print(f"🎉 Base de données {db_path} mise à jour avec succès!")
                
            except Exception as e:
                print(f"❌ Erreur lors de la mise à jour de {db_path}: {e}")
                if 'conn' in locals():
                    conn.close()
        else:
            print(f"⚠️ Base de données non trouvée: {db_path}")

if __name__ == "__main__":
    print("🚀 GESTION DES FORMATIONS - Correction de la base de données")
    print("=" * 60)
    fix_database()
    print("\n✅ Correction terminée!")
    input("Appuyez sur Entrée pour continuer...")
