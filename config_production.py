"""
ملف تكوين الإنتاج - GESTION DES FORMATIONS
مُحسن للشبكة المحلية وقاعدة البيانات المركزية
يدعم جميع إصدارات Windows (7-11) و 32/64 بت
"""

import os
import sys
import secrets
from pathlib import Path

class ProductionConfig:
    """إعدادات الإنتاج للشبكة المحلية وعدة المستخدمين"""

    # الأمان
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة

    # تحديد مسار التطبيق
    if getattr(sys, 'frozen', False):
        # إذا كان التطبيق مجمد (EXE)
        basedir = os.path.dirname(sys.executable)
    else:
        # إذا كان يعمل من Python
        basedir = os.path.abspath(os.path.dirname(__file__))

    # قاعدة البيانات المركزية
    data_dir = os.path.join(basedir, 'data')
    os.makedirs(data_dir, exist_ok=True)

    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        f'sqlite:///{os.path.join(data_dir, "gestion_formations.db")}'

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,          # فحص الاتصال قبل الاستخدام
        'pool_recycle': 300,            # إعادة تدوير الاتصالات كل 5 دقائق
        'pool_timeout': 30,             # مهلة انتظار الاتصال
        'connect_args': {
            'check_same_thread': False,  # مهم لـ SQLite مع threads متعددة
            'timeout': 30,              # مهلة الاتصال
            'isolation_level': None     # تفعيل autocommit
        }
    }

    # إعدادات Flask
    DEBUG = False
    TESTING = False

    # رفع الملفات
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB كحد أقصى
    UPLOAD_FOLDER = os.path.join(basedir, 'uploads')

    # النسخ الاحتياطية
    BACKUP_FOLDER = os.path.join(basedir, 'backups')

    # السجلات
    LOG_FOLDER = os.path.join(basedir, 'logs')

    # الأداء
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # سنة واحدة للملفات الثابتة

    # دعم عدة مستخدمين
    THREADED = True
    PROCESSES = 1

    # أمان الشبكة المحلية
    SESSION_COOKIE_SECURE = False       # False للـ HTTP المحلي
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = 3600 * 8  # 8 ساعات

    # إعدادات الشبكة المحلية
    NETWORK_CONFIG = {
        'host': '0.0.0.0',              # السماح بالوصول من جميع العناوين
        'port': 5000,                   # المنفذ الافتراضي
        'threaded': True,               # دعم عدة مستخدمين
        'use_reloader': False,          # إيقاف إعادة التحميل
        'debug': False                  # إيقاف وضع التطوير
    }

    # إعدادات النسخ الاحتياطي التلقائي
    BACKUP_CONFIG = {
        'auto_backup': True,            # تفعيل النسخ الاحتياطي التلقائي
        'backup_interval': 24,          # كل 24 ساعة
        'max_backups': 30,              # الاحتفاظ بـ 30 نسخة احتياطية
        'backup_on_startup': True       # نسخة احتياطية عند البدء
    }

    # معلومات التطبيق
    APP_INFO = {
        'name': 'GESTION DES FORMATIONS',
        'version': '1.0.0',
        'description': 'نظام إدارة التكوين والتدريب',
        'company': 'ABOULFADEL.A',
        'logo': 'Formation-continue-1024x1024.png',
        'icon': 'app_icon.ico'
    }
    
    @staticmethod
    def init_app(app):
        """Initialiser l'application avec cette configuration"""
        
        # Créer les dossiers nécessaires
        folders = [
            ProductionConfig.UPLOAD_FOLDER,
            ProductionConfig.BACKUP_FOLDER,
            ProductionConfig.LOG_FOLDER
        ]
        
        for folder in folders:
            if not os.path.exists(folder):
                os.makedirs(folder)
        
        # Configuration des logs
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            # Log des erreurs
            error_log = os.path.join(ProductionConfig.LOG_FOLDER, 'errors.log')
            error_handler = RotatingFileHandler(
                error_log, maxBytes=10240000, backupCount=10
            )
            error_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            error_handler.setLevel(logging.ERROR)
            app.logger.addHandler(error_handler)
            
            # Log général
            info_log = os.path.join(ProductionConfig.LOG_FOLDER, 'app.log')
            info_handler = RotatingFileHandler(
                info_log, maxBytes=10240000, backupCount=10
            )
            info_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s'
            ))
            info_handler.setLevel(logging.INFO)
            app.logger.addHandler(info_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('GESTION DES FORMATIONS - Démarrage en mode production')

# Configuration par défaut
Config = ProductionConfig
