('C:\\Users\\<USER>\\Desktop\\Getion des formation '
 'jadid\\dist\\GESTION_DES_FORMATIONS.exe',
 False,
 False,
 False,
 ['C:\\Users\\<USER>\\Desktop\\Getion des formation '
  'jadid\\app/static/images/app_icon.ico'],
 None,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Desktop\\Getion des formation '
 'jadid\\build\\gestion_formations_final\\GESTION_DES_FORMATIONS.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('run_production',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\run_production.py',
   'PYSOURCE'),
  ('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\greenlet\\_greenlet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('app.db',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app.db',
   'DATA'),
  ('app\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\__init__.py',
   'DATA'),
  ('app\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\forms.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\forms.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\models.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\models.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\routes.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\forms.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\forms.py',
   'DATA'),
  ('app\\models.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\models.py',
   'DATA'),
  ('app\\routes.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\routes.py',
   'DATA'),
  ('app\\static\\images\\5218235.jpg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\5218235.jpg',
   'DATA'),
  ('app\\static\\images\\5218235.svg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\5218235.svg',
   'DATA'),
  ('app\\static\\images\\Formation-continue-1024x1024.png',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\Formation-continue-1024x1024.png',
   'DATA'),
  ('app\\static\\images\\app_icon.ico',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\app_icon.ico',
   'DATA'),
  ('app\\static\\images\\favicon.ico',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\favicon.ico',
   'DATA'),
  ('app\\static\\images\\grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg',
   'DATA'),
  ('app\\static\\images\\login-illustration.svg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\login-illustration.svg',
   'DATA'),
  ('app\\static\\js\\delete-handler.js',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\js\\delete-handler.js',
   'DATA'),
  ('app\\static\\uploads\\logos\\Formation-continue-1024x1024.png',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\uploads\\logos\\Formation-continue-1024x1024.png',
   'DATA'),
  ('app\\templates\\activity\\log.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\activity\\log.html',
   'DATA'),
  ('app\\templates\\agenda\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\edit.html',
   'DATA'),
  ('app\\templates\\agenda\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\index.html',
   'DATA'),
  ('app\\templates\\agenda\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\new.html',
   'DATA'),
  ('app\\templates\\agenda\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\print.html',
   'DATA'),
  ('app\\templates\\backup\\import_simple.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\import_simple.html',
   'DATA'),
  ('app\\templates\\backup\\list.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\list.html',
   'DATA'),
  ('app\\templates\\backup\\settings.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\settings.html',
   'DATA'),
  ('app\\templates\\backup\\simple.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\simple.html',
   'DATA'),
  ('app\\templates\\base.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\base.html',
   'DATA'),
  ('app\\templates\\company\\info.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\company\\info.html',
   'DATA'),
  ('app\\templates\\dashboard.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dashboard.html',
   'DATA'),
  ('app\\templates\\domaines\\edit_domaine.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\edit_domaine.html',
   'DATA'),
  ('app\\templates\\domaines\\edit_theme.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\edit_theme.html',
   'DATA'),
  ('app\\templates\\domaines\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\index.html',
   'DATA'),
  ('app\\templates\\domaines\\new_domaine.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\new_domaine.html',
   'DATA'),
  ('app\\templates\\domaines\\new_theme.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\new_theme.html',
   'DATA'),
  ('app\\templates\\dossiers\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers\\edit.html',
   'DATA'),
  ('app\\templates\\dossiers\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers\\index.html',
   'DATA'),
  ('app\\templates\\dossiers\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers\\new.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\edit.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\index.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\new.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\print.html',
   'DATA'),
  ('app\\templates\\eligibilite_ofppt.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\eligibilite_ofppt.html',
   'DATA'),
  ('app\\templates\\fiches\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches\\edit.html',
   'DATA'),
  ('app\\templates\\fiches\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches\\index.html',
   'DATA'),
  ('app\\templates\\fiches\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches\\new.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\edit.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\index.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\new.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\print.html',
   'DATA'),
  ('app\\templates\\formateurs\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\edit.html',
   'DATA'),
  ('app\\templates\\formateurs\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\index.html',
   'DATA'),
  ('app\\templates\\formateurs\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\new.html',
   'DATA'),
  ('app\\templates\\formateurs\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\print.html',
   'DATA'),
  ('app\\templates\\formations\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formations\\edit.html',
   'DATA'),
  ('app\\templates\\formations\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formations\\index.html',
   'DATA'),
  ('app\\templates\\login.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\login.html',
   'DATA'),
  ('app\\templates\\organismes\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\edit.html',
   'DATA'),
  ('app\\templates\\organismes\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\index.html',
   'DATA'),
  ('app\\templates\\organismes\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\new.html',
   'DATA'),
  ('app\\templates\\organismes\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\print.html',
   'DATA'),
  ('app\\templates\\partials\\search_form.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\partials\\search_form.html',
   'DATA'),
  ('app\\templates\\print_layout.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\print_layout.html',
   'DATA'),
  ('app\\templates\\rapports\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\rapports\\index.html',
   'DATA'),
  ('app\\templates\\rapports\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\rapports\\print.html',
   'DATA'),
  ('app\\templates\\remboursements\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\edit.html',
   'DATA'),
  ('app\\templates\\remboursements\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\index.html',
   'DATA'),
  ('app\\templates\\remboursements\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\new.html',
   'DATA'),
  ('app\\templates\\remboursements\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\print.html',
   'DATA'),
  ('app\\templates\\reports\\report.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\reports\\report.html',
   'DATA'),
  ('app\\templates\\search_results.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\search_results.html',
   'DATA'),
  ('app\\templates\\users\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\edit.html',
   'DATA'),
  ('app\\templates\\users\\forgot_password.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\forgot_password.html',
   'DATA'),
  ('app\\templates\\users\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\index.html',
   'DATA'),
  ('app\\templates\\users\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\new.html',
   'DATA'),
  ('config.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\config.py',
   'DATA'),
  ('config_production.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\config_production.py',
   'DATA'),
  ('flask\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\flask\\py.typed',
   'DATA'),
  ('jinja2\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\jinja2\\py.typed',
   'DATA'),
  ('wtforms\\locale\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\README.md',
   'DATA'),
  ('wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ar\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\bg\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ca\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\cs_CZ\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\cy\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\de\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\de\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\de\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\de\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\de_CH\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\el\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\el\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\el\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\el\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\en\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\en\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\en\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\en\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\es\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\es\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\es\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\es\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\et\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\et\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\et\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\et\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\fa\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\fi\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\fr\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\he\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\he\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\he\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\he\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\hu\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\it\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\it\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\it\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\it\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ja\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ko\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\nb\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\nl\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\pl\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\pt\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\ru\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\sk\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\sv\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\tr\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\uk\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\wtforms.pot',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\wtforms.pot',
   'DATA'),
  ('wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.mo',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.mo',
   'DATA'),
  ('wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.po',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\wtforms\\locale\\zh_TW\\LC_MESSAGES\\wtforms.po',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\gestion_formations_final\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1751968101,
 [('runw.exe',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll')
