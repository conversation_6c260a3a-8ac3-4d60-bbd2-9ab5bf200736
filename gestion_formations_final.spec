# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين PyInstaller - GESTION DES FORMATIONS
يدعم جميع إصدارات Windows والشبكة المحلية
"""

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# مسار المشروع
project_path = os.path.abspath('.')

# جمع ملفات Flask
flask_datas = collect_data_files('flask')
jinja2_datas = collect_data_files('jinja2')
wtforms_datas = collect_data_files('wtforms')

# ملفات التطبيق
app_datas = [
    ('app', 'app'),
    ('config.py', '.'),
    ('config_production.py', '.'),
]

# إضافة قاعدة البيانات إذا كانت موجودة
if os.path.exists('app.db'):
    app_datas.append(('app.db', '.'))

# جمع جميع الملفات
all_datas = flask_datas + jinja2_datas + wtforms_datas + app_datas

# المكتبات المخفية
hiddenimports = [
    'flask', 'flask.app', 'flask.blueprints', 'flask.globals',
    'flask.helpers', 'flask.json', 'flask.logging', 'flask.sessions',
    'flask.signals', 'flask.templating', 'flask.testing', 'flask.views',
    'flask.wrappers', 'werkzeug', 'werkzeug.serving', 'werkzeug.utils',
    'werkzeug.exceptions', 'werkzeug.routing', 'werkzeug.security',
    'jinja2', 'jinja2.ext', 'jinja2.loaders', 'jinja2.runtime',
    'jinja2.utils', 'sqlalchemy', 'sqlalchemy.dialects.sqlite',
    'sqlalchemy.engine', 'sqlalchemy.orm', 'sqlalchemy.pool',
    'flask_sqlalchemy', 'flask_login', 'flask_wtf', 'wtforms',
    'wtforms.fields', 'wtforms.validators', 'wtforms.widgets',
    'email_validator', 'sqlite3', 'threading', 'webbrowser',
    'socket', 'time', 'datetime', 'json', 'uuid', 'hashlib',
    'secrets', 'pathlib', 'shutil'
]

# تحليل الملف الرئيسي
a = Analysis(
    ['run_production.py'],
    pathex=[project_path],
    binaries=[],
    datas=all_datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy',
        'PIL.ImageTk', 'PIL.ImageWin', 'test', 'unittest',
        'doctest', 'pdb', 'pydoc', 'distutils'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات المكررة
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GESTION_DES_FORMATIONS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app/static/images/app_icon.ico',
    version_info={
        'version': '*******',
        'description': 'GESTION DES FORMATIONS - نظام إدارة التكوين',
        'product_name': 'GESTION DES FORMATIONS',
        'product_version': '1.0.0',
        'company_name': 'ABOULFADEL.A',
        'file_description': 'نظام إدارة التكوين والتدريب',
        'internal_name': 'GestionFormations',
        'copyright': '© 2024 ABOULFADEL.A',
        'original_filename': 'GESTION_DES_FORMATIONS.exe',
    }
)
