{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-folder-open"></i> Dossiers Administratif
                        </h4>
                        <a href="{{ url_for('add_dossier_administratif') }}" class="btn btn-light">
                            <i class="fas fa-plus"></i> Nouveau Dossier
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if dossiers %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Entreprise</th>
                                        <th>F1</th>
                                        <th>RC</th>
                                        <th>Statut</th>
                                        <th>Déclaration sur l'honneur</th>
                                        <th>Pv</th>
                                        <th>Procuration</th>
                                        <th>Attestation de RIB</th>
                                        <th>Date de création</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dossier in dossiers %}
                                    <tr>
                                        <td>{{ dossier.id }}</td>
                                        <td>
                                            <strong>{{ dossier.fiche_inscription.nom_entreprise }}</strong>
                                        </td>
                                        <td>
                                            {% if dossier.f1 %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dossier.rc %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dossier.statut %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dossier.declaration_sur_honneur %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dossier.pv %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dossier.procuration %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dossier.attestation_rib %}
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            {% else %}
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                            {% endif %}
                                        </td>
                                        <td>{{ dossier.date_creation.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('edit_dossier_administratif', id=dossier.id) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('delete_dossier_administratif', id=dossier.id) }}" 
                                                   class="btn btn-sm btn-outline-danger" 
                                                   onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce dossier administratif ?')"
                                                   title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun dossier administratif trouvé</h5>
                            <p class="text-muted">Commencez par créer votre premier dossier administratif.</p>
                            <a href="{{ url_for('add_dossier_administratif') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Créer un dossier administratif
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
