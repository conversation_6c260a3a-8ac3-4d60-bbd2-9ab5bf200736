@echo off
chcp 65001 > nul
title اختبار الملف التنفيذي - GESTION DES FORMATIONS

echo.
echo ========================================
echo   اختبار الملف التنفيذي
echo   GESTION DES FORMATIONS
echo   ABOULFADEL.A
echo ========================================
echo.

echo 🧪 هذا السكريبت سيختبر الملف التنفيذي
echo.

if not exist "GESTION_DES_FORMATIONS_Distribution\GESTION_DES_FORMATIONS.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo 💡 تأكد من تشغيل عملية البناء أولاً
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود
echo.

echo 📊 معلومات الملف:
for %%F in ("GESTION_DES_FORMATIONS_Distribution\GESTION_DES_FORMATIONS.exe") do (
    echo    📍 المسار: %%~fF
    echo    📊 الحجم: %%~zF bytes
    echo    📅 التاريخ: %%~tF
)
echo.

echo 🚀 اختبار تشغيل الملف التنفيذي...
echo ⚠️  سيتم إغلاق الاختبار بعد 10 ثوان
echo.

cd GESTION_DES_FORMATIONS_Distribution

echo 🔄 بدء التشغيل...
timeout /t 2 /nobreak > nul

start "" GESTION_DES_FORMATIONS.exe

echo ✅ تم تشغيل الملف التنفيذي
echo.
echo 💡 تحقق من:
echo    • فتح المتصفح تلقائياً
echo    • عرض صفحة تسجيل الدخول
echo    • عدم ظهور رسائل خطأ
echo.
echo 📋 إذا ظهرت مشاكل:
echo    • راجع ملف app.log
echo    • جرب تشغيل تشغيل_النظام.bat
echo    • تأكد من صلاحيات المدير
echo.

echo ⏳ انتظار 10 ثوان للاختبار...
timeout /t 10 /nobreak

echo.
echo 🏁 انتهى الاختبار
echo 💡 إذا فتح المتصفح وظهرت صفحة تسجيل الدخول، فالملف التنفيذي يعمل بشكل صحيح!
echo.

pause
