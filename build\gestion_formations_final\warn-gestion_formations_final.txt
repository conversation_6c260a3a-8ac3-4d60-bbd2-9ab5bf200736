
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\Desktop\Getion des formation jadid\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\Desktop\Getion des formation jadid\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), webbrowser (delayed), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), werkzeug._reloader (delayed, optional), tty (top-level), click._termui_impl (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by werkzeug._internal (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), setuptools._distutils.dist (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named 'distutils.util' - imported by setuptools._core_metadata (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (top-level), setuptools._distutils.extension (delayed), setuptools._distutils.compilers.C.unix (delayed, conditional)
missing module named 'distutils.fancy_getopt' - imported by setuptools.dist (top-level), setuptools._distutils.compilers.C.base (delayed), setuptools._distutils.cmd (delayed)
missing module named 'distutils.debug' - imported by setuptools.dist (top-level), setuptools._distutils.compilers.C.base (delayed), setuptools._distutils.cmd (delayed), setuptools._distutils.filelist (delayed)
excluded module named distutils - imported by setuptools.discovery (top-level), setuptools.errors (top-level), setuptools.command.bdist_egg (top-level), setuptools.command.sdist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (delayed), setuptools.installer (top-level), setuptools._shutil (top-level), setuptools.command.bdist_wheel (top-level), setuptools._distutils.util (delayed, conditional), setuptools._distutils.command.build_ext (delayed), setuptools._distutils.command.sdist (top-level), setuptools._distutils.compilers.C.cygwin (delayed)
missing module named 'unittest.mock' - imported by setuptools._distutils.compilers.C.msvc (top-level)
missing module named 'distutils.text_file' - imported by setuptools._distutils.extension (delayed), setuptools._distutils.sysconfig (delayed)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils.sysconfig' - imported by setuptools._distutils.extension (delayed)
missing module named 'distutils.versionpredicate' - imported by setuptools._distutils.dist (delayed)
missing module named 'distutils.command' - imported by setuptools.command (top-level), setuptools.dist (top-level), setuptools.command.build (top-level), setuptools.command.sdist (top-level), setuptools._distutils.dist (delayed)
missing module named 'distutils.cmd' - imported by setuptools.dist (top-level), setuptools._distutils.dist (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by code (delayed, conditional, optional), flask.cli (delayed, conditional, optional), rlcompleter (optional), site (delayed, optional)
excluded module named pydoc - imported by werkzeug.debug.repr (delayed), _sitebuiltins (delayed)
missing module named 'distutils.spawn' - imported by setuptools._distutils.cmd (delayed)
missing module named 'distutils.dist' - imported by setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (top-level), setuptools._distutils.cmd (delayed, conditional)
missing module named 'distutils.errors' - imported by setuptools.config.expand (top-level), setuptools.extension (top-level), setuptools.dist (top-level), setuptools.archive_util (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.installer (top-level), setuptools (top-level), setuptools.msvc (top-level)
missing module named 'distutils.core' - imported by setuptools.extension (top-level), setuptools.dist (top-level), setuptools (top-level), setuptools._distutils.dist (delayed)
missing module named 'distutils.extension' - imported by setuptools.extension (top-level)
missing module named 'distutils.filelist' - imported by setuptools.monkey (top-level), setuptools.command.egg_info (top-level)
missing module named 'distutils.dir_util' - imported by setuptools.command.bdist_egg (top-level)
missing module named 'distutils.log' - imported by setuptools.logging (top-level), setuptools.dist (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named wmi - imported by dns.win32util (conditional)
missing module named pythoncom - imported by dns.win32util (conditional)
missing module named httpx - imported by dns._trio_backend (conditional), dns.query (conditional), dns.asyncquery (conditional), dns._asyncio_backend (conditional)
missing module named 'httpcore._backends' - imported by dns._trio_backend (conditional), dns.query (conditional), dns._asyncio_backend (conditional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named trio - imported by dns._trio_backend (top-level), dns.quic (conditional), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named sniffio - imported by dns.asyncbackend (delayed, optional)
missing module named httpcore - imported by dns._trio_backend (conditional), dns._asyncio_backend (conditional)
missing module named anyio - imported by dns._asyncio_backend (conditional)
missing module named 'trio.socket' - imported by dns._trio_backend (top-level)
missing module named babel - imported by wtforms.fields.numeric (delayed, optional), flask_wtf.i18n (top-level)
missing module named '_typeshed.wsgi' - imported by werkzeug._internal (conditional), werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional)
missing module named flask_babel - imported by flask_wtf.i18n (top-level)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named pymysql - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named 'cryptography.hazmat' - imported by werkzeug.serving (delayed, optional)
missing module named 'cryptography.x509' - imported by werkzeug.serving (delayed, conditional, optional)
missing module named cryptography - imported by werkzeug.serving (delayed, conditional, optional), flask.cli (delayed, conditional, optional)
missing module named dotenv - imported by flask.cli (delayed, optional)
missing module named blinker - imported by flask.signals (optional)
missing module named asgiref - imported by flask.app (delayed, optional)
