('C:\\Users\\<USER>\\Desktop\\Getion des formation '
 'jadid\\build\\GESTION_DES_FORMATIONS\\GESTION_DES_FORMATIONS.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('run_exe',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\run_exe.py',
   'PYSOURCE'),
  ('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\greenlet\\_greenlet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('app\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\__init__.py',
   'DATA'),
  ('app\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\forms.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\forms.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\models.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\models.cpython-311.pyc',
   'DATA'),
  ('app\\__pycache__\\routes.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\__pycache__\\routes.cpython-311.pyc',
   'DATA'),
  ('app\\forms.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\forms.py',
   'DATA'),
  ('app\\models.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\models.py',
   'DATA'),
  ('app\\routes.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\app\\routes.py',
   'DATA'),
  ('app\\static\\images\\5218235.jpg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\5218235.jpg',
   'DATA'),
  ('app\\static\\images\\5218235.svg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\5218235.svg',
   'DATA'),
  ('app\\static\\images\\Formation-continue-1024x1024.png',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\Formation-continue-1024x1024.png',
   'DATA'),
  ('app\\static\\images\\app_icon.ico',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\app_icon.ico',
   'DATA'),
  ('app\\static\\images\\favicon.ico',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\favicon.ico',
   'DATA'),
  ('app\\static\\images\\grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg',
   'DATA'),
  ('app\\static\\images\\login-illustration.svg',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\images\\login-illustration.svg',
   'DATA'),
  ('app\\static\\js\\delete-handler.js',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\js\\delete-handler.js',
   'DATA'),
  ('app\\static\\uploads\\logos\\Formation-continue-1024x1024.png',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\static\\uploads\\logos\\Formation-continue-1024x1024.png',
   'DATA'),
  ('app\\templates\\activity\\log.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\activity\\log.html',
   'DATA'),
  ('app\\templates\\agenda\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\edit.html',
   'DATA'),
  ('app\\templates\\agenda\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\index.html',
   'DATA'),
  ('app\\templates\\agenda\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\new.html',
   'DATA'),
  ('app\\templates\\agenda\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\agenda\\print.html',
   'DATA'),
  ('app\\templates\\backup\\import_simple.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\import_simple.html',
   'DATA'),
  ('app\\templates\\backup\\list.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\list.html',
   'DATA'),
  ('app\\templates\\backup\\settings.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\settings.html',
   'DATA'),
  ('app\\templates\\backup\\simple.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\backup\\simple.html',
   'DATA'),
  ('app\\templates\\base.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\base.html',
   'DATA'),
  ('app\\templates\\company\\info.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\company\\info.html',
   'DATA'),
  ('app\\templates\\dashboard.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dashboard.html',
   'DATA'),
  ('app\\templates\\domaines\\edit_domaine.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\edit_domaine.html',
   'DATA'),
  ('app\\templates\\domaines\\edit_theme.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\edit_theme.html',
   'DATA'),
  ('app\\templates\\domaines\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\index.html',
   'DATA'),
  ('app\\templates\\domaines\\new_domaine.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\new_domaine.html',
   'DATA'),
  ('app\\templates\\domaines\\new_theme.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\domaines\\new_theme.html',
   'DATA'),
  ('app\\templates\\dossiers\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers\\edit.html',
   'DATA'),
  ('app\\templates\\dossiers\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers\\index.html',
   'DATA'),
  ('app\\templates\\dossiers\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers\\new.html',
   'DATA'),
  ('app\\templates\\dossiers_administratif\\add.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_administratif\\add.html',
   'DATA'),
  ('app\\templates\\dossiers_administratif\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_administratif\\edit.html',
   'DATA'),
  ('app\\templates\\dossiers_administratif\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_administratif\\index.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\edit.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\index.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\new.html',
   'DATA'),
  ('app\\templates\\dossiers_techniques\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\dossiers_techniques\\print.html',
   'DATA'),
  ('app\\templates\\eligibilite_ofppt.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\eligibilite_ofppt.html',
   'DATA'),
  ('app\\templates\\fiches\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches\\edit.html',
   'DATA'),
  ('app\\templates\\fiches\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches\\index.html',
   'DATA'),
  ('app\\templates\\fiches\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches\\new.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\edit.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\index.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\new.html',
   'DATA'),
  ('app\\templates\\fiches_inscription\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\fiches_inscription\\print.html',
   'DATA'),
  ('app\\templates\\formateurs\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\edit.html',
   'DATA'),
  ('app\\templates\\formateurs\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\index.html',
   'DATA'),
  ('app\\templates\\formateurs\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\new.html',
   'DATA'),
  ('app\\templates\\formateurs\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formateurs\\print.html',
   'DATA'),
  ('app\\templates\\formations\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formations\\edit.html',
   'DATA'),
  ('app\\templates\\formations\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\formations\\index.html',
   'DATA'),
  ('app\\templates\\login.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\login.html',
   'DATA'),
  ('app\\templates\\organismes\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\edit.html',
   'DATA'),
  ('app\\templates\\organismes\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\index.html',
   'DATA'),
  ('app\\templates\\organismes\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\new.html',
   'DATA'),
  ('app\\templates\\organismes\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\organismes\\print.html',
   'DATA'),
  ('app\\templates\\partials\\search_form.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\partials\\search_form.html',
   'DATA'),
  ('app\\templates\\print_layout.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\print_layout.html',
   'DATA'),
  ('app\\templates\\rapports\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\rapports\\index.html',
   'DATA'),
  ('app\\templates\\rapports\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\rapports\\print.html',
   'DATA'),
  ('app\\templates\\remboursements\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\edit.html',
   'DATA'),
  ('app\\templates\\remboursements\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\index.html',
   'DATA'),
  ('app\\templates\\remboursements\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\new.html',
   'DATA'),
  ('app\\templates\\remboursements\\print.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\remboursements\\print.html',
   'DATA'),
  ('app\\templates\\reports\\report.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\reports\\report.html',
   'DATA'),
  ('app\\templates\\search_results.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\search_results.html',
   'DATA'),
  ('app\\templates\\users\\edit.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\edit.html',
   'DATA'),
  ('app\\templates\\users\\forgot_password.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\forgot_password.html',
   'DATA'),
  ('app\\templates\\users\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\index.html',
   'DATA'),
  ('app\\templates\\users\\new.html',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\app\\templates\\users\\new.html',
   'DATA'),
  ('config_production.py',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation jadid\\config_production.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Getion des formation '
   'jadid\\build\\GESTION_DES_FORMATIONS\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
