; ===============================================
; Inno Setup Script for GESTION DES FORMATIONS
; Système de gestion des formations - ABOULFADEL.A
; Compatible avec toutes les versions Windows (7-11) et 32/64 bits
; ===============================================

[Setup]
; معلومات التطبيق
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName=GESTION DES FORMATIONS
AppVersion=1.0.0
AppVerName=GESTION DES FORMATIONS 1.0.0
AppPublisher=ABOULFADEL.A
AppCopyright=© 2024 ABOULFADEL.A
AppComments=Système de gestion des formations avec support réseau local

; مسارات التثبيت
DefaultDirName={autopf}\GESTION DES FORMATIONS
DefaultGroupName=GESTION DES FORMATIONS
AllowNoIcons=yes
CreateAppDir=yes

; ملف الإخراج
OutputDir=Output
OutputBaseFilename=GESTION_DES_FORMATIONS_Setup_v1.0.0
SetupIconFile=app\static\images\app_icon.ico
UninstallDisplayIcon={app}\GESTION_DES_FORMATIONS.exe
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=6.1sp1
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

; إعدادات المثبت
PrivilegesRequired=admin
DisableProgramGroupPage=no
UsePreviousAppDir=yes
AlwaysRestart=no

; إعدادات واجهة المثبت
DisableWelcomePage=no
DisableFinishedPage=no

; إعدادات اللغة
ShowLanguageDialog=auto

[Languages]
Name: "arabic"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Créer une icône sur le bureau"; GroupDescription: "Icônes supplémentaires"
Name: "firewall"; Description: "Ajouter une exception au pare-feu Windows (requis pour le réseau local)"; GroupDescription: "Paramètres réseau"

[Files]
; الملف التنفيذي الرئيسي
Source: "GESTION_DES_FORMATIONS_Distribution\GESTION_DES_FORMATIONS.exe"; DestDir: "{app}"; Flags: ignoreversion

; ملفات مساعدة
Source: "GESTION_DES_FORMATIONS_Distribution\تشغيل_النظام.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "GESTION_DES_FORMATIONS_Distribution\lisez-moi.txt"; DestDir: "{app}"; Flags: ignoreversion

; الشعار والأيقونات
Source: "app\static\images\Formation-continue-1024x1024.png"; DestDir: "{app}\images"; Flags: ignoreversion
Source: "app\static\images\app_icon.ico"; DestDir: "{app}\images"; Flags: ignoreversion

[Icons]
; أيقونات قائمة البرامج
Name: "{group}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Comment: "Système de gestion des formations"
Name: "{group}\Lancer le système"; Filename: "{app}\تشغيل_النظام.bat"; IconFilename: "{app}\images\app_icon.ico"; Comment: "Lancer avec informations réseau"
Name: "{group}\Guide d'utilisation"; Filename: "{app}\lisez-moi.txt"; Comment: "Guide complet d'utilisation"
Name: "{group}\{cm:UninstallProgram,GESTION DES FORMATIONS}"; Filename: "{uninstallexe}"; Comment: "Désinstaller le programme"

; Icône bureau
Name: "{autodesktop}\GESTION DES FORMATIONS"; Filename: "{app}\GESTION_DES_FORMATIONS.exe"; IconFilename: "{app}\images\app_icon.ico"; Tasks: desktopicon; Comment: "Système de gestion des formations"

[Registry]
; تسجيل التطبيق في النظام
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"
Root: HKLM; Subkey: "Software\ABOULFADEL.A\GESTION DES FORMATIONS"; ValueType: string; ValueName: "Publisher"; ValueData: "ABOULFADEL.A"

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\GESTION_DES_FORMATIONS.exe"; Description: "تشغيل GESTION DES FORMATIONS الآن"; Flags: nowait postinstall skipifsilent unchecked

; فتح دليل الاستخدام
Filename: "{app}\اقرأني.txt"; Description: "فتح دليل الاستخدام"; Flags: postinstall skipifsilent unchecked shellexec

; إضافة استثناءات جدار الحماية
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS"" dir=in action=allow program=""{app}\GESTION_DES_FORMATIONS.exe"" enable=yes"; Flags: runhidden; Tasks: firewall
Filename: "netsh"; Parameters: "advfirewall firewall add rule name=""GESTION DES FORMATIONS Port"" dir=in action=allow protocol=TCP localport=5000 enable=yes"; Flags: runhidden; Tasks: firewall

[UninstallRun]
; إزالة قواعد جدار الحماية عند إلغاء التثبيت
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS"""; Flags: runhidden
Filename: "netsh"; Parameters: "advfirewall firewall delete rule name=""GESTION DES FORMATIONS Port"""; Flags: runhidden

[Dirs]
; إنشاء مجلدات البيانات
Name: "{app}"; Permissions: users-full
Name: "{app}\data"; Permissions: users-full
Name: "{app}\backups"; Permissions: users-full
Name: "{app}\logs"; Permissions: users-full
Name: "{app}\uploads"; Permissions: users-full
Name: "{app}\images"; Permissions: users-full

[Code]
// كود Pascal للمثبت

// فحص إصدار Windows
function IsWindowsVersionSupported: Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  // Windows 7 SP1 أو أحدث
  Result := (Version.Major > 6) or 
           ((Version.Major = 6) and (Version.Minor >= 1) and (Version.ServicePackMajor >= 1));
end;

// تهيئة المثبت
function InitializeSetup: Boolean;
begin
  Result := True;
  
  if not IsWindowsVersionSupported then
  begin
    MsgBox('هذا البرنامج يتطلب Windows 7 SP1 أو إصدار أحدث.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  // رسالة ترحيب
  if MsgBox('مرحباً بك في مثبت GESTION DES FORMATIONS' + #13#10 + 
           'نظام إدارة التكوين والتدريب مع دعم الشبكة المحلية' + #13#10 + #13#10 +
           'المميزات:' + #13#10 +
           '• يدعم جميع إصدارات Windows (7-11)' + #13#10 +
           '• يدعم الشبكة المحلية وعدة مستخدمين' + #13#10 +
           '• قاعدة بيانات مركزية' + #13#10 +
           '• لا يحتاج برامج إضافية' + #13#10 + #13#10 +
           'هل تريد المتابعة؟', mbConfirmation, MB_YESNO) = IDNO then
  begin
    Result := False;
  end;
end;

// معالجة خطوات التثبيت
procedure CurStepChanged(CurStep: TSetupStep);
var
  ConfigContent: String;
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء ملف تكوين
    ConfigContent := '[GESTION_DES_FORMATIONS]' + #13#10 +
                    'Version=1.0.0' + #13#10 +
                    'InstallDate=' + GetDateTimeString('yyyy-mm-dd hh:nn:ss', #0, #0) + #13#10 +
                    'Publisher=ABOULFADEL.A' + #13#10 +
                    'InstallPath=' + ExpandConstant('{app}') + #13#10 +
                    'NetworkEnabled=true' + #13#10 +
                    'DefaultPort=5000' + #13#10;
    
    SaveStringToFile(ExpandConstant('{app}\data\config.ini'), ConfigContent, False);
  end;
end;

// معالجة تغيير الصفحات
procedure CurPageChanged(CurPageID: Integer);
begin
  // صفحة النهاية
  if CurPageID = wpFinished then
  begin
    WizardForm.FinishedLabel.Caption := 
      'تم تثبيت GESTION DES FORMATIONS بنجاح!' + #13#10 + #13#10 +
      'يمكنك الآن تشغيل البرنامج من:' + #13#10 +
      '• قائمة البرامج: GESTION DES FORMATIONS' + #13#10 +
      '• سطح المكتب (إذا اخترت إنشاء أيقونة)' + #13#10 + #13#10 +
      'للوصول من الشبكة المحلية:' + #13#10 +
      '1. شغل البرنامج على هذا الجهاز (الخادم)' + #13#10 +
      '2. استخدم عنوان IP المعروض للوصول من أجهزة أخرى' + #13#10 + #13#10 +
      'المستخدم الافتراضي: admin / admin123';
  end;
end;
