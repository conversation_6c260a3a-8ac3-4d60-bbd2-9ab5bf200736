{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit"></i> Modifier le Dossier Administratif
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-4">
                                    {{ form.fiche_inscription.label(class="form-label fw-bold") }}
                                    {{ form.fiche_inscription(class="form-select form-select-lg", style="font-size: 1.1rem; padding: 12px 16px;") }}
                                    {% if form.fiche_inscription.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.fiche_inscription.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">Sélectionnez l'entreprise pour ce dossier administratif</small>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-file-alt"></i> Documents Administratifs</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            {{ form.f1(class="form-check-input") }}
                                            {{ form.f1.label(class="form-check-label") }}
                                            {% if form.f1.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.f1.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            {{ form.rc(class="form-check-input") }}
                                            {{ form.rc.label(class="form-check-label") }}
                                            {% if form.rc.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.rc.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            {{ form.statut(class="form-check-input") }}
                                            {{ form.statut.label(class="form-check-label") }}
                                            {% if form.statut.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.statut.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            {{ form.declaration_sur_honneur(class="form-check-input") }}
                                            {{ form.declaration_sur_honneur.label(class="form-check-label") }}
                                            {% if form.declaration_sur_honneur.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.declaration_sur_honneur.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            {{ form.pv(class="form-check-input") }}
                                            {{ form.pv.label(class="form-check-label") }}
                                            {% if form.pv.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.pv.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            {{ form.procuration(class="form-check-input") }}
                                            {{ form.procuration.label(class="form-check-label") }}
                                            {% if form.procuration.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.procuration.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            {{ form.attestation_rib(class="form-check-input") }}
                                            {{ form.attestation_rib.label(class="form-check-label") }}
                                            {% if form.attestation_rib.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.attestation_rib.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations supplémentaires</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            {{ form.observations.label(class="form-label") }}
                                            {{ form.observations(class="form-control", rows="3") }}
                                            {% if form.observations.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.observations.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            {{ form.piece_jointe.label(class="form-label") }}
                                            {{ form.piece_jointe(class="form-control") }}
                                            {% if form.piece_jointe.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.piece_jointe.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                            <small class="form-text text-muted">Formats acceptés: PDF, JPG, PNG</small>
                                        </div>
                                    </div>
                                </div>

                                {% if dossier.piece_jointe %}
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-paperclip"></i>
                                            Fichier actuel: <strong>{{ dossier.piece_jointe }}</strong>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('dossiers_administratif') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
