"""
Script pour tester si toutes les mises à jour sont présentes
"""

import os
import sys
import sqlite3

def test_database_updates():
    """Tester les mises à jour de la base de données"""
    print("🔍 Test des mises à jour de la base de données...")
    
    # Tester la base de données principale
    db_path = "app.db"
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test dossier_administratif
        cursor.execute("PRAGMA table_info(dossier_administratif)")
        admin_columns = [col[1] for col in cursor.fetchall()]
        print(f"✅ dossier_administratif colonnes: {admin_columns}")
        
        required_admin = ['observations', 'piece_jointe']
        for col in required_admin:
            if col in admin_columns:
                print(f"  ✅ {col} présent")
            else:
                print(f"  ❌ {col} MANQUANT")
        
        # Test dossier_technique
        cursor.execute("PRAGMA table_info(dossier_technique)")
        tech_columns = [col[1] for col in cursor.fetchall()]
        print(f"✅ dossier_technique colonnes: {tech_columns}")
        
        required_tech = ['m1', 'f2', 'f3']
        for col in required_tech:
            if col in tech_columns:
                print(f"  ✅ {col} présent")
            else:
                print(f"  ❌ {col} MANQUANT")
        
        # Test dossier_remboursement
        cursor.execute("PRAGMA table_info(dossier_remboursement)")
        rembours_columns = [col[1] for col in cursor.fetchall()]
        print(f"✅ dossier_remboursement colonnes: {rembours_columns}")
        
        if 'f3' in rembours_columns:
            print(f"  ✅ f3 présent")
        else:
            print(f"  ❌ f3 MANQUANT")
        
        conn.close()
    else:
        print("❌ Base de données app.db non trouvée")

def test_template_updates():
    """Tester les mises à jour des templates"""
    print("\n🔍 Test des mises à jour des templates...")
    
    # Test navigation dans base.html
    base_path = "app/templates/base.html"
    if os.path.exists(base_path):
        with open(base_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Vérifier l'ordre de navigation
        if 'eligibilite_ofppt' in content and 'fiches_inscription' in content:
            # Trouver les positions
            eligibilite_pos = content.find('eligibilite_ofppt')
            fiches_pos = content.find('fiches_inscription')
            
            if eligibilite_pos < fiches_pos:
                print("  ✅ Éligibilité OFPPT avant Fiches d'Inscription")
            else:
                print("  ❌ Ordre navigation incorrect")
        
        if 'dossiers_administratif' in content:
            print("  ✅ Dossiers Administratif dans navigation")
        else:
            print("  ❌ Dossiers Administratif MANQUANT")
    else:
        print("❌ base.html non trouvé")
    
    # Test dossiers administratif templates
    admin_add_path = "app/templates/dossiers_administratif/add.html"
    if os.path.exists(admin_add_path):
        with open(admin_add_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'form-select-lg' in content:
            print("  ✅ Champ Entreprise agrandi")
        else:
            print("  ❌ Champ Entreprise PAS agrandi")
            
        if 'observations' in content:
            print("  ✅ Champ observations présent")
        else:
            print("  ❌ Champ observations MANQUANT")
            
        if 'piece_jointe' in content:
            print("  ✅ Champ piece_jointe présent")
        else:
            print("  ❌ Champ piece_jointe MANQUANT")
    else:
        print("❌ dossiers_administratif/add.html non trouvé")
    
    # Test dossiers techniques templates
    tech_new_path = "app/templates/dossiers_techniques/new.html"
    if os.path.exists(tech_new_path):
        with open(tech_new_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_fields = ['form.m1', 'form.f2', 'form.f3']
        for field in required_fields:
            if field in content:
                print(f"  ✅ {field} présent")
            else:
                print(f"  ❌ {field} MANQUANT")
    else:
        print("❌ dossiers_techniques/new.html non trouvé")

def test_models_updates():
    """Tester les mises à jour des modèles"""
    print("\n🔍 Test des mises à jour des modèles...")
    
    models_path = "app/models.py"
    if os.path.exists(models_path):
        with open(models_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Test DossierAdministratif
        if 'class DossierAdministratif' in content:
            print("  ✅ DossierAdministratif classe présente")
            
            if 'observations = db.Column' in content:
                print("  ✅ observations colonne présente")
            else:
                print("  ❌ observations colonne MANQUANTE")
                
            if 'piece_jointe = db.Column' in content:
                print("  ✅ piece_jointe colonne présente")
            else:
                print("  ❌ piece_jointe colonne MANQUANTE")
        else:
            print("  ❌ DossierAdministratif classe MANQUANTE")
        
        # Test DossierTechnique updates
        if 'm1 = db.Column' in content:
            print("  ✅ m1 colonne présente")
        else:
            print("  ❌ m1 colonne MANQUANTE")
    else:
        print("❌ models.py non trouvé")

def test_forms_updates():
    """Tester les mises à jour des formulaires"""
    print("\n🔍 Test des mises à jour des formulaires...")
    
    forms_path = "app/forms.py"
    if os.path.exists(forms_path):
        with open(forms_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Test DossierAdministratifForm
        if 'class DossierAdministratifForm' in content:
            print("  ✅ DossierAdministratifForm présent")
            
            if 'observations = TextAreaField' in content:
                print("  ✅ observations field présent")
            else:
                print("  ❌ observations field MANQUANT")
                
            if 'piece_jointe = FileField' in content:
                print("  ✅ piece_jointe field présent")
            else:
                print("  ❌ piece_jointe field MANQUANT")
        else:
            print("  ❌ DossierAdministratifForm MANQUANT")
        
        # Test DossierTechniqueForm updates
        tech_fields = ['m1 = BooleanField', 'f2 = BooleanField', 'f3 = BooleanField']
        for field in tech_fields:
            if field in content:
                print(f"  ✅ {field} présent")
            else:
                print(f"  ❌ {field} MANQUANT")
    else:
        print("❌ forms.py non trouvé")

def main():
    print("🚀 TEST DES MISES À JOUR - GESTION DES FORMATIONS")
    print("=" * 60)
    
    test_database_updates()
    test_template_updates()
    test_models_updates()
    test_forms_updates()
    
    print("\n" + "=" * 60)
    print("✅ Test terminé!")

if __name__ == "__main__":
    main()
    input("\nAppuyez sur Entrée pour continuer...")
